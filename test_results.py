#!/usr/bin/env python3
"""
测试结果生成器

生成测试结果并保存到文件
"""

import sys
import os
import traceback
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, 'src')

def write_result(message):
    """写入测试结果"""
    with open('test_output.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
    print(message)

def run_tests():
    """运行所有测试"""
    # 清空结果文件
    with open('test_output.txt', 'w', encoding='utf-8') as f:
        f.write(f"LangGraph Agent测试结果 - {datetime.now()}\n")
        f.write("=" * 60 + "\n\n")
    
    write_result("🚀 开始LangGraph Agent测试")
    
    test_results = []
    
    # 测试1: 基础模块导入
    write_result("\n📦 测试1: 基础模块导入")
    try:
        from src.core.logger import get_logger
        write_result("✅ logger模块导入成功")
        
        from src.agents.common.base import AgentBase, WorkflowAgent
        write_result("✅ 通用基类导入成功")
        
        from src.agents.common.services import LLMService, DatabaseService, RedisService
        write_result("✅ 通用服务层导入成功")
        
        from src.agents.common.workflow import BaseAgentState, EventEmitter
        write_result("✅ 通用工作流组件导入成功")
        
        test_results.append(("基础模块导入", True))
        
    except Exception as e:
        write_result(f"❌ 基础模块导入失败: {e}")
        test_results.append(("基础模块导入", False))
    
    # 测试2: 数据模型
    write_result("\n📊 测试2: 数据模型")
    try:
        from src.models.travel_models import TravelPlanRequest, POI, DailyPlan, TravelItinerary
        write_result("✅ 旅行模型导入成功")
        
        # 测试模型创建
        request = TravelPlanRequest(
            trace_id="test_001",
            user_id="test_user",
            query="测试查询"
        )
        write_result("✅ TravelPlanRequest创建成功")
        
        test_results.append(("数据模型", True))
        
    except Exception as e:
        write_result(f"❌ 数据模型测试失败: {e}")
        test_results.append(("数据模型", False))
    
    # 测试3: 核心模块
    write_result("\n🧠 测试3: 核心模块")
    try:
        from src.agents.travel_planner.core import IntentProcessor, POIAnalyzer, ItineraryComposer
        write_result("✅ 核心业务模块导入成功")
        
        # 创建实例
        intent_processor = IntentProcessor()
        write_result("✅ IntentProcessor创建成功")
        
        poi_analyzer = POIAnalyzer()
        write_result("✅ POIAnalyzer创建成功")
        
        itinerary_composer = ItineraryComposer()
        write_result("✅ ItineraryComposer创建成功")
        
        test_results.append(("核心模块", True))
        
    except Exception as e:
        write_result(f"❌ 核心模块测试失败: {e}")
        write_result(f"详细错误: {traceback.format_exc()}")
        test_results.append(("核心模块", False))
    
    # 测试4: 工作流组件
    write_result("\n⚙️ 测试4: 工作流组件")
    try:
        from src.agents.travel_planner.workflow import (
            TravelPlannerState, 
            create_travel_planner_state,
            TravelPlannerNodes,
            TravelPlannerWorkflow
        )
        write_result("✅ 工作流模块导入成功")
        
        # 创建状态
        state = create_travel_planner_state(
            trace_id="test_trace",
            user_id="test_user",
            agent_id="test_agent",
            original_query="测试查询"
        )
        write_result("✅ 状态创建成功")
        
        # 创建节点
        nodes = TravelPlannerNodes()
        write_result("✅ 节点创建成功")
        
        # 创建工作流
        workflow = TravelPlannerWorkflow()
        write_result("✅ 工作流创建成功")
        
        test_results.append(("工作流组件", True))
        
    except Exception as e:
        write_result(f"❌ 工作流组件测试失败: {e}")
        write_result(f"详细错误: {traceback.format_exc()}")
        test_results.append(("工作流组件", False))
    
    # 测试5: 主Agent
    write_result("\n🤖 测试5: 主Agent")
    try:
        from src.agents.travel_planner import LangGraphTravelPlannerAgent
        write_result("✅ LangGraphTravelPlannerAgent导入成功")
        
        # 创建Agent实例
        agent = LangGraphTravelPlannerAgent()
        write_result(f"✅ Agent创建成功: {agent.agent_id}")
        
        # 测试基本方法
        agent_info = agent.get_agent_info()
        write_result(f"✅ Agent信息获取成功: {agent_info.get('agent_type')}")
        
        test_results.append(("主Agent", True))
        
    except Exception as e:
        write_result(f"❌ 主Agent测试失败: {e}")
        write_result(f"详细错误: {traceback.format_exc()}")
        test_results.append(("主Agent", False))
    
    # 测试6: LangGraph检查
    write_result("\n📚 测试6: LangGraph检查")
    try:
        import langgraph
        write_result("✅ LangGraph已安装")
        test_results.append(("LangGraph", True))
    except ImportError:
        write_result("❌ LangGraph未安装")
        write_result("   建议运行: pip install langgraph")
        test_results.append(("LangGraph", False))
    
    # 结果汇总
    write_result("\n" + "=" * 60)
    write_result("📊 测试结果汇总")
    write_result("=" * 60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        write_result(f"{test_name}: {status}")
        if result:
            passed += 1
    
    write_result(f"\n📈 总体结果: {passed}/{len(test_results)} 测试通过")
    
    if passed == len(test_results):
        write_result("🎉 所有测试通过！新架构工作正常。")
        write_result("\n🚀 下一步建议:")
        write_result("1. 测试实际工作流执行")
        write_result("2. 进行集成测试")
        write_result("3. 性能测试和优化")
    elif passed >= len(test_results) - 2:
        write_result("⚠️ 大部分测试通过，架构基本可用，需要修复少量问题。")
        write_result("\n🔧 建议:")
        write_result("1. 安装缺失的依赖")
        write_result("2. 修复导入问题")
    else:
        write_result("❌ 多个测试失败，需要进一步调试和修复。")
        write_result("\n🔍 建议:")
        write_result("1. 检查Python环境和依赖")
        write_result("2. 查看详细错误信息")
        write_result("3. 逐步修复导入问题")
    
    write_result(f"\n✅ 测试完成，结果已保存到 test_output.txt")

if __name__ == "__main__":
    try:
        run_tests()
    except Exception as e:
        write_result(f"\n💥 测试执行异常: {e}")
        write_result(f"详细错误: {traceback.format_exc()}")
