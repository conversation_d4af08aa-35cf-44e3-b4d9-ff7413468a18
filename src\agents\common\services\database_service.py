"""
数据库服务封装

为Agent提供统一的数据库操作接口，包括MySQL和MongoDB
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from src.database.mysql_client import fetch_one, fetch_all, execute_update, test_connection
from src.database.mongodb_client import get_mongo_client
from src.core.logger import get_logger

logger = get_logger(__name__)


class DatabaseService:
    """Agent专用数据库服务封装"""
    
    def __init__(self):
        """初始化数据库服务"""
        self.logger = logger
        self._mongo_client = None
        
    async def _get_mongo_client(self):
        """获取MongoDB客户端"""
        if self._mongo_client is None:
            self._mongo_client = await get_mongo_client()
        return self._mongo_client
        
    # ==================== MySQL操作 ====================
    
    async def mysql_fetch_one(
        self, 
        query: str, 
        params: tuple = None
    ) -> Optional[Dict[str, Any]]:
        """
        执行MySQL查询并返回一条记录
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Optional[Dict[str, Any]]: 查询结果
        """
        try:
            result = await fetch_one(query, params)
            self.logger.debug(f"MySQL查询成功: {query[:50]}...")
            return result
            
        except Exception as e:
            self.logger.error(f"MySQL查询失败: {query[:50]}..., 错误: {str(e)}")
            return None
            
    async def mysql_fetch_all(
        self, 
        query: str, 
        params: tuple = None
    ) -> List[Dict[str, Any]]:
        """
        执行MySQL查询并返回所有记录
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            result = await fetch_all(query, params)
            self.logger.debug(f"MySQL查询成功: {query[:50]}..., 返回{len(result)}条记录")
            return result
            
        except Exception as e:
            self.logger.error(f"MySQL查询失败: {query[:50]}..., 错误: {str(e)}")
            return []
            
    async def mysql_execute(
        self, 
        query: str, 
        params: tuple = None
    ) -> int:
        """
        执行MySQL更新/插入/删除操作
        
        Args:
            query: SQL语句
            params: 参数
            
        Returns:
            int: 影响的行数
        """
        try:
            affected_rows = await execute_update(query, params)
            self.logger.debug(f"MySQL执行成功: {query[:50]}..., 影响{affected_rows}行")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"MySQL执行失败: {query[:50]}..., 错误: {str(e)}")
            return 0
            
    # ==================== MongoDB操作 ====================
    
    async def mongo_insert_one(
        self, 
        collection: str, 
        document: Dict[str, Any]
    ) -> Optional[str]:
        """
        向MongoDB插入一条文档
        
        Args:
            collection: 集合名称
            document: 文档数据
            
        Returns:
            Optional[str]: 插入的文档ID
        """
        try:
            client = await self._get_mongo_client()
            collection_obj = client.get_collection(collection)
            result = await collection_obj.insert_one(document)
            
            doc_id = str(result.inserted_id)
            self.logger.debug(f"MongoDB插入成功: {collection}, ID: {doc_id}")
            return doc_id
            
        except Exception as e:
            self.logger.error(f"MongoDB插入失败: {collection}, 错误: {str(e)}")
            return None
            
    async def mongo_find_one(
        self, 
        collection: str, 
        filter_dict: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        从MongoDB查询一条文档
        
        Args:
            collection: 集合名称
            filter_dict: 查询条件
            
        Returns:
            Optional[Dict[str, Any]]: 查询结果
        """
        try:
            client = await self._get_mongo_client()
            collection_obj = client.get_collection(collection)
            result = await collection_obj.find_one(filter_dict)
            
            if result:
                # 转换ObjectId为字符串
                if '_id' in result:
                    result['_id'] = str(result['_id'])
                    
            self.logger.debug(f"MongoDB查询成功: {collection}")
            return result
            
        except Exception as e:
            self.logger.error(f"MongoDB查询失败: {collection}, 错误: {str(e)}")
            return None
            
    async def mongo_find_many(
        self, 
        collection: str, 
        filter_dict: Dict[str, Any],
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        从MongoDB查询多条文档
        
        Args:
            collection: 集合名称
            filter_dict: 查询条件
            limit: 限制返回数量
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            client = await self._get_mongo_client()
            collection_obj = client.get_collection(collection)
            cursor = collection_obj.find(filter_dict).limit(limit)
            
            results = []
            async for doc in cursor:
                # 转换ObjectId为字符串
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                results.append(doc)
                
            self.logger.debug(f"MongoDB查询成功: {collection}, 返回{len(results)}条记录")
            return results
            
        except Exception as e:
            self.logger.error(f"MongoDB查询失败: {collection}, 错误: {str(e)}")
            return []
            
    async def mongo_update_one(
        self, 
        collection: str, 
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any]
    ) -> bool:
        """
        更新MongoDB中的一条文档
        
        Args:
            collection: 集合名称
            filter_dict: 查询条件
            update_dict: 更新数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            client = await self._get_mongo_client()
            collection_obj = client.get_collection(collection)
            result = await collection_obj.update_one(filter_dict, {"$set": update_dict})
            
            success = result.modified_count > 0
            self.logger.debug(f"MongoDB更新: {collection}, 成功: {success}")
            return success
            
        except Exception as e:
            self.logger.error(f"MongoDB更新失败: {collection}, 错误: {str(e)}")
            return False
            
    # ==================== 用户相关操作 ====================
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户画像
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 用户画像数据
        """
        try:
            # 从MySQL获取基本用户信息
            user_query = "SELECT * FROM users WHERE user_id = %s"
            user_data = await self.mysql_fetch_one(user_query, (user_id,))
            
            if not user_data:
                return None
                
            # 从MongoDB获取用户记忆和偏好
            memories = await self.mongo_find_many(
                "memories", 
                {"user_id": user_id},
                limit=50
            )
            
            return {
                "user_data": user_data,
                "memories": memories,
                "updated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取用户画像失败: {user_id}, 错误: {str(e)}")
            return None
            
    async def save_user_memory(
        self, 
        user_id: str, 
        memory_data: Dict[str, Any]
    ) -> bool:
        """
        保存用户记忆
        
        Args:
            user_id: 用户ID
            memory_data: 记忆数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            memory_doc = {
                "user_id": user_id,
                "memory_type": memory_data.get("memory_type", "travel_preference"),
                "content": memory_data.get("content", {}),
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            result = await self.mongo_insert_one("memories", memory_doc)
            success = result is not None
            
            if success:
                self.logger.info(f"用户记忆保存成功: {user_id}")
            else:
                self.logger.error(f"用户记忆保存失败: {user_id}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"保存用户记忆失败: {user_id}, 错误: {str(e)}")
            return False
            
    # ==================== 健康检查 ====================
    
    async def health_check(self) -> Dict[str, bool]:
        """
        数据库服务健康检查
        
        Returns:
            Dict[str, bool]: 各数据库的健康状态
        """
        mysql_healthy = False
        mongo_healthy = False
        
        try:
            # 检查MySQL
            mysql_healthy = await test_connection()
            
            # 检查MongoDB
            client = await self._get_mongo_client()
            await client.client.admin.command('ping')
            mongo_healthy = True
            
        except Exception as e:
            self.logger.error(f"数据库健康检查失败: {str(e)}")
            
        return {
            "mysql": mysql_healthy,
            "mongodb": mongo_healthy,
            "overall": mysql_healthy and mongo_healthy
        }
