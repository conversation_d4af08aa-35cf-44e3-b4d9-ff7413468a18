#!/usr/bin/env python3
"""
快速导入测试

直接测试关键模块的导入情况
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, 'src')

print("🔍 开始快速导入测试...")
print(f"Python版本: {sys.version}")
print(f"工作目录: {os.getcwd()}")

# 测试1: 基础模块
print("\n1. 测试基础模块...")
try:
    from src.core.logger import get_logger
    print("✅ logger模块导入成功")
except Exception as e:
    print(f"❌ logger模块导入失败: {e}")

try:
    from src.agents.common.base import AgentBase
    print("✅ AgentBase导入成功")
except Exception as e:
    print(f"❌ AgentBase导入失败: {e}")

# 测试2: 服务层
print("\n2. 测试服务层...")
try:
    from src.agents.common.services import LLMService
    print("✅ LLMService导入成功")
except Exception as e:
    print(f"❌ LLMService导入失败: {e}")

# 测试3: 数据模型
print("\n3. 测试数据模型...")
try:
    from src.models.travel_models import TravelPlanRequest
    print("✅ TravelPlanRequest导入成功")
except Exception as e:
    print(f"❌ TravelPlanRequest导入失败: {e}")

# 测试4: 核心模块
print("\n4. 测试核心模块...")
try:
    from src.agents.travel_planner.core import IntentProcessor
    print("✅ IntentProcessor导入成功")
except Exception as e:
    print(f"❌ IntentProcessor导入失败: {e}")

# 测试5: LangGraph
print("\n5. 测试LangGraph...")
try:
    import langgraph
    print("✅ LangGraph已安装")
except ImportError:
    print("❌ LangGraph未安装")

# 测试6: 主Agent
print("\n6. 测试主Agent...")
try:
    from src.agents.travel_planner import LangGraphTravelPlannerAgent
    print("✅ LangGraphTravelPlannerAgent导入成功")
except Exception as e:
    print(f"❌ LangGraphTravelPlannerAgent导入失败: {e}")

print("\n✅ 快速导入测试完成")
