"""
事件发射器

为Agent提供统一的流式事件发射功能，支持SSE流式输出
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from enum import Enum

from src.core.logger import get_logger

logger = get_logger(__name__)


class EventType(str, Enum):
    """事件类型枚举"""
    # 进度事件
    PROGRESS = "progress"
    
    # 思考过程事件
    THINKING_START = "thinking_start"
    THINKING_STEP = "thinking_step"
    THINKING_END = "thinking_end"
    
    # 工具调用事件
    TOOL_CALL_START = "tool_call_start"
    TOOL_CALL_END = "tool_call_end"
    TOOL_RESULT = "tool_result"
    
    # 步骤事件
    STEP_START = "step_start"
    STEP_END = "step_end"
    STEP_RESULT = "step_result"
    
    # 最终结果事件
    FINAL_RESULT = "final_result"
    
    # 错误事件
    ERROR = "error"
    WARNING = "warning"
    
    # 系统事件
    SYSTEM_INFO = "system_info"
    HEALTH_CHECK = "health_check"


class EventEmitter:
    """Agent流式事件发射器"""
    
    def __init__(self, session_id: Optional[str] = None, agent_id: Optional[str] = None):
        """
        初始化事件发射器
        
        Args:
            session_id: 会话ID
            agent_id: Agent ID
        """
        self.session_id = session_id or str(uuid.uuid4())
        self.agent_id = agent_id or "unknown_agent"
        self.logger = logger
        self._event_queue = asyncio.Queue()
        self._is_active = True
        
    def _create_event(
        self,
        event_type: EventType,
        data: Dict[str, Any],
        message: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建标准化事件对象
        
        Args:
            event_type: 事件类型
            data: 事件数据
            message: 事件消息
            
        Returns:
            Dict[str, Any]: 标准化事件对象
        """
        event = {
            "event_id": str(uuid.uuid4()),
            "event_type": event_type.value,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "timestamp": time.time(),
            "iso_timestamp": datetime.now().isoformat(),
            "data": data
        }
        
        if message:
            event["message"] = message
            
        return event
        
    async def emit(
        self,
        event_type: EventType,
        data: Dict[str, Any],
        message: Optional[str] = None
    ) -> None:
        """
        发射事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            message: 事件消息
        """
        if not self._is_active:
            return
            
        try:
            event = self._create_event(event_type, data, message)
            await self._event_queue.put(event)
            
            self.logger.debug(f"事件发射: {event_type.value}, session: {self.session_id}")
            
        except Exception as e:
            self.logger.error(f"事件发射失败: {event_type.value}, 错误: {str(e)}")
            
    # ==================== 进度事件 ====================
    
    async def emit_progress(
        self,
        message: str,
        progress: int,
        step: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        发射进度事件
        
        Args:
            message: 进度消息
            progress: 进度百分比 (0-100)
            step: 当前步骤
            extra_data: 额外数据
        """
        data = {
            "progress": max(0, min(100, progress)),  # 确保在0-100范围内
            "step": step
        }
        
        if extra_data:
            data.update(extra_data)
            
        await self.emit(EventType.PROGRESS, data, message)
        
    # ==================== 思考过程事件 ====================
    
    async def emit_thinking_start(
        self,
        phase: str,
        description: Optional[str] = None
    ) -> None:
        """
        发射思考开始事件
        
        Args:
            phase: 思考阶段
            description: 阶段描述
        """
        data = {"phase": phase}
        if description:
            data["description"] = description
            
        await self.emit(EventType.THINKING_START, data, f"开始思考: {phase}")
        
    async def emit_thinking_step(
        self,
        step: str,
        reasoning: str,
        category: Optional[str] = None
    ) -> None:
        """
        发射思考步骤事件
        
        Args:
            step: 思考步骤
            reasoning: 推理过程
            category: 思考类别
        """
        data = {
            "step": step,
            "reasoning": reasoning
        }
        
        if category:
            data["category"] = category
            
        await self.emit(EventType.THINKING_STEP, data, f"思考步骤: {step}")
        
    async def emit_thinking_end(
        self,
        phase: str,
        conclusion: Optional[str] = None
    ) -> None:
        """
        发射思考结束事件
        
        Args:
            phase: 思考阶段
            conclusion: 结论
        """
        data = {"phase": phase}
        if conclusion:
            data["conclusion"] = conclusion
            
        await self.emit(EventType.THINKING_END, data, f"思考完成: {phase}")
        
    # ==================== 工具调用事件 ====================
    
    async def emit_tool_call_start(
        self,
        tool_name: str,
        parameters: Dict[str, Any]
    ) -> None:
        """
        发射工具调用开始事件
        
        Args:
            tool_name: 工具名称
            parameters: 调用参数
        """
        data = {
            "tool_name": tool_name,
            "parameters": parameters
        }
        
        await self.emit(EventType.TOOL_CALL_START, data, f"开始调用工具: {tool_name}")
        
    async def emit_tool_call_end(
        self,
        tool_name: str,
        success: bool,
        duration: Optional[float] = None
    ) -> None:
        """
        发射工具调用结束事件
        
        Args:
            tool_name: 工具名称
            success: 是否成功
            duration: 调用耗时（秒）
        """
        data = {
            "tool_name": tool_name,
            "success": success
        }
        
        if duration is not None:
            data["duration"] = duration
            
        status = "成功" if success else "失败"
        await self.emit(EventType.TOOL_CALL_END, data, f"工具调用{status}: {tool_name}")
        
    async def emit_tool_result(
        self,
        tool_name: str,
        result: Dict[str, Any],
        summary: Optional[str] = None
    ) -> None:
        """
        发射工具结果事件
        
        Args:
            tool_name: 工具名称
            result: 工具结果
            summary: 结果摘要
        """
        data = {
            "tool_name": tool_name,
            "result": result
        }
        
        if summary:
            data["summary"] = summary
            
        await self.emit(EventType.TOOL_RESULT, data, f"工具结果: {tool_name}")
        
    # ==================== 步骤事件 ====================
    
    async def emit_step_start(
        self,
        step_name: str,
        description: Optional[str] = None
    ) -> None:
        """
        发射步骤开始事件
        
        Args:
            step_name: 步骤名称
            description: 步骤描述
        """
        data = {"step_name": step_name}
        if description:
            data["description"] = description
            
        await self.emit(EventType.STEP_START, data, f"开始执行: {step_name}")
        
    async def emit_step_end(
        self,
        step_name: str,
        success: bool,
        duration: Optional[float] = None
    ) -> None:
        """
        发射步骤结束事件
        
        Args:
            step_name: 步骤名称
            success: 是否成功
            duration: 执行耗时（秒）
        """
        data = {
            "step_name": step_name,
            "success": success
        }
        
        if duration is not None:
            data["duration"] = duration
            
        status = "成功" if success else "失败"
        await self.emit(EventType.STEP_END, data, f"步骤{status}: {step_name}")
        
    async def emit_step_result(
        self,
        step_name: str,
        result: Dict[str, Any],
        summary: Optional[str] = None
    ) -> None:
        """
        发射步骤结果事件
        
        Args:
            step_name: 步骤名称
            result: 步骤结果
            summary: 结果摘要
        """
        data = {
            "step_name": step_name,
            "result": result
        }
        
        if summary:
            data["summary"] = summary
            
        await self.emit(EventType.STEP_RESULT, data, f"步骤结果: {step_name}")
        
    # ==================== 最终结果和错误事件 ====================
    
    async def emit_final_result(
        self,
        result: Dict[str, Any],
        summary: Optional[str] = None
    ) -> None:
        """
        发射最终结果事件
        
        Args:
            result: 最终结果
            summary: 结果摘要
        """
        data = {"result": result}
        if summary:
            data["summary"] = summary
            
        await self.emit(EventType.FINAL_RESULT, data, "任务完成")
        
    async def emit_error(
        self,
        error: str,
        step: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        发射错误事件
        
        Args:
            error: 错误信息
            step: 发生错误的步骤
            details: 错误详情
        """
        data = {"error": error}
        if step:
            data["step"] = step
        if details:
            data["details"] = details
            
        await self.emit(EventType.ERROR, data, f"错误: {error}")
        
    async def emit_warning(
        self,
        warning: str,
        step: Optional[str] = None
    ) -> None:
        """
        发射警告事件
        
        Args:
            warning: 警告信息
            step: 发生警告的步骤
        """
        data = {"warning": warning}
        if step:
            data["step"] = step
            
        await self.emit(EventType.WARNING, data, f"警告: {warning}")
        
    # ==================== 事件流管理 ====================
    
    async def get_event_stream(self) -> AsyncGenerator[Dict[str, Any], None]:
        """
        获取事件流
        
        Yields:
            Dict[str, Any]: 事件对象
        """
        while self._is_active:
            try:
                # 等待事件，设置超时避免无限等待
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                yield event
                
            except asyncio.TimeoutError:
                # 超时继续循环，允许检查_is_active状态
                continue
            except Exception as e:
                self.logger.error(f"事件流获取失败: {str(e)}")
                break
                
    def stop(self) -> None:
        """停止事件发射器"""
        self._is_active = False
        self.logger.info(f"事件发射器已停止: {self.session_id}")
        
    def is_active(self) -> bool:
        """检查事件发射器是否活跃"""
        return self._is_active
        
    def get_queue_size(self) -> int:
        """获取事件队列大小"""
        return self._event_queue.qsize()
