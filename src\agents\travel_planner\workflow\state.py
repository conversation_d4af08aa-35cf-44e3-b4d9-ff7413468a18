"""
旅行规划工作流状态定义

定义LangGraph状态图中使用的状态模型
"""

from typing import Dict, Any, List, Optional, TypedDict
from datetime import datetime

from src.agents.common.workflow import BaseAgentState


class TravelPlannerState(BaseAgentState, total=False):
    """
    旅行规划Agent的状态定义
    
    继承自BaseAgentState，添加旅行规划特有的状态字段
    """
    # 意图理解结果
    extracted_entities: Dict[str, Any]  # 提取的实体信息
    
    # 用户画像和记忆
    user_profile: Optional[Dict[str, Any]]  # 用户画像
    user_memories: Optional[List[Dict[str, Any]]]  # 用户记忆
    
    # 工具调用结果
    tool_results: Dict[str, Any]  # 工具调用结果存储
    
    # POI相关数据
    poi_search_results: Dict[str, Any]  # POI搜索结果
    scored_pois: List[Dict[str, Any]]  # 评分后的POI列表
    
    # 行程编排结果
    orchestrated_itinerary: Optional[Dict[str, Any]]  # 编排后的行程
    route_results: Dict[str, Any]  # 路线规划结果
    
    # 天气和预算
    weather_forecast: List[Dict[str, Any]]  # 天气预报
    budget_estimation: Optional[Dict[str, Any]]  # 预算估算
    
    # 最终结果
    final_itinerary: Optional[Dict[str, Any]]  # 最终行程对象


def create_travel_planner_state(
    trace_id: str,
    user_id: str,
    agent_id: str,
    original_query: str,
    **kwargs
) -> TravelPlannerState:
    """
    创建旅行规划Agent状态
    
    Args:
        trace_id: 追踪ID
        user_id: 用户ID
        agent_id: Agent实例ID
        original_query: 原始查询
        **kwargs: 其他状态字段
        
    Returns:
        TravelPlannerState: 旅行规划状态对象
    """
    now = datetime.now().isoformat()
    
    state = TravelPlannerState(
        # 基础字段
        trace_id=trace_id,
        user_id=user_id,
        agent_id=agent_id,
        original_query=original_query,
        current_step="init",
        created_at=now,
        updated_at=now,
        context={},
        intermediate_results={},
        final_result=None,
        errors=[],
        warnings=[],
        metadata={},
        
        # 旅行规划特有字段
        extracted_entities={},
        user_profile=None,
        user_memories=None,
        tool_results={},
        poi_search_results={},
        scored_pois=[],
        orchestrated_itinerary=None,
        route_results={},
        weather_forecast=[],
        budget_estimation=None,
        final_itinerary=None
    )
    
    # 添加额外的字段
    state.update(kwargs)
    
    return state


def update_travel_state_step(state: TravelPlannerState, step: str) -> TravelPlannerState:
    """
    更新旅行规划状态的当前步骤
    
    Args:
        state: 当前状态
        step: 新的步骤名称
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["current_step"] = step
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def add_tool_result(
    state: TravelPlannerState, 
    tool_name: str, 
    result: Any
) -> TravelPlannerState:
    """
    向状态添加工具调用结果
    
    Args:
        state: 当前状态
        tool_name: 工具名称
        result: 工具结果
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    
    if "tool_results" not in updated_state:
        updated_state["tool_results"] = {}
        
    updated_state["tool_results"][tool_name] = result
    updated_state["updated_at"] = datetime.now().isoformat()
    
    return updated_state


def set_extracted_entities(
    state: TravelPlannerState, 
    entities: Dict[str, Any]
) -> TravelPlannerState:
    """
    设置提取的实体信息
    
    Args:
        state: 当前状态
        entities: 实体信息
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["extracted_entities"] = entities
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def set_user_profile(
    state: TravelPlannerState, 
    profile: Dict[str, Any]
) -> TravelPlannerState:
    """
    设置用户画像
    
    Args:
        state: 当前状态
        profile: 用户画像
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["user_profile"] = profile
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def set_poi_results(
    state: TravelPlannerState, 
    poi_results: Dict[str, Any],
    scored_pois: Optional[List[Dict[str, Any]]] = None
) -> TravelPlannerState:
    """
    设置POI搜索和评分结果
    
    Args:
        state: 当前状态
        poi_results: POI搜索结果
        scored_pois: 评分后的POI列表
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["poi_search_results"] = poi_results
    
    if scored_pois is not None:
        updated_state["scored_pois"] = scored_pois
        
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def set_orchestrated_itinerary(
    state: TravelPlannerState, 
    itinerary_data: Dict[str, Any]
) -> TravelPlannerState:
    """
    设置编排后的行程
    
    Args:
        state: 当前状态
        itinerary_data: 行程数据
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["orchestrated_itinerary"] = itinerary_data
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def set_final_itinerary(
    state: TravelPlannerState, 
    final_itinerary: Dict[str, Any]
) -> TravelPlannerState:
    """
    设置最终行程
    
    Args:
        state: 当前状态
        final_itinerary: 最终行程对象
        
    Returns:
        TravelPlannerState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["final_itinerary"] = final_itinerary
    updated_state["final_result"] = final_itinerary
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def get_state_summary(state: TravelPlannerState) -> Dict[str, Any]:
    """
    获取状态摘要信息
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 状态摘要
    """
    return {
        "trace_id": state.get("trace_id"),
        "user_id": state.get("user_id"),
        "agent_id": state.get("agent_id"),
        "current_step": state.get("current_step"),
        "created_at": state.get("created_at"),
        "updated_at": state.get("updated_at"),
        "has_extracted_entities": bool(state.get("extracted_entities")),
        "has_user_profile": state.get("user_profile") is not None,
        "poi_search_count": len(state.get("poi_search_results", {})),
        "scored_pois_count": len(state.get("scored_pois", [])),
        "has_orchestrated_itinerary": state.get("orchestrated_itinerary") is not None,
        "has_final_itinerary": state.get("final_itinerary") is not None,
        "error_count": len(state.get("errors", [])),
        "warning_count": len(state.get("warnings", []))
    }
