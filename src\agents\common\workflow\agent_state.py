"""
通用Agent状态定义

定义所有Agent共享的基础状态模型
"""

from typing import Dict, Any, List, Optional, TypedDict
from datetime import datetime
from pydantic import BaseModel, Field


class BaseAgentState(TypedDict, total=False):
    """
    基础Agent状态定义
    
    这是一个TypedDict，用于LangGraph状态图中的状态传递
    所有Agent的状态都应该继承或包含这些基础字段
    """
    # 基础标识信息
    trace_id: str  # 追踪ID
    user_id: str   # 用户ID
    agent_id: str  # Agent实例ID
    
    # 请求信息
    original_query: str  # 原始用户查询
    current_step: str    # 当前执行步骤
    
    # 时间信息
    created_at: str      # 创建时间
    updated_at: str      # 更新时间
    
    # 状态数据
    context: Dict[str, Any]      # 上下文数据
    intermediate_results: Dict[str, Any]  # 中间结果
    final_result: Optional[Dict[str, Any]]  # 最终结果
    
    # 错误处理
    errors: List[Dict[str, Any]]  # 错误列表
    warnings: List[str]           # 警告列表
    
    # 元数据
    metadata: Dict[str, Any]      # 元数据


class AgentStateModel(BaseModel):
    """
    Agent状态的Pydantic模型版本
    
    用于数据验证和序列化，与TypedDict版本保持一致
    """
    # 基础标识信息
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    agent_id: str = Field(..., description="Agent实例ID")
    
    # 请求信息
    original_query: str = Field(..., description="原始用户查询")
    current_step: str = Field(default="init", description="当前执行步骤")
    
    # 时间信息
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="更新时间")
    
    # 状态数据
    context: Dict[str, Any] = Field(default_factory=dict, description="上下文数据")
    intermediate_results: Dict[str, Any] = Field(default_factory=dict, description="中间结果")
    final_result: Optional[Dict[str, Any]] = Field(None, description="最终结果")
    
    # 错误处理
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    def to_typed_dict(self) -> BaseAgentState:
        """转换为TypedDict格式，用于LangGraph"""
        return BaseAgentState(**self.model_dump())
        
    @classmethod
    def from_typed_dict(cls, state: BaseAgentState) -> "AgentStateModel":
        """从TypedDict创建模型实例"""
        return cls(**state)
        
    def update_step(self, step: str) -> None:
        """更新当前步骤"""
        self.current_step = step
        self.updated_at = datetime.now().isoformat()
        
    def add_error(self, error: str, step: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> None:
        """添加错误信息"""
        error_info = {
            "error": error,
            "step": step or self.current_step,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.errors.append(error_info)
        
    def add_warning(self, warning: str) -> None:
        """添加警告信息"""
        self.warnings.append(f"[{datetime.now().isoformat()}] {warning}")
        
    def set_context(self, key: str, value: Any) -> None:
        """设置上下文数据"""
        self.context[key] = value
        self.updated_at = datetime.now().isoformat()
        
    def get_context(self, key: str, default: Any = None) -> Any:
        """获取上下文数据"""
        return self.context.get(key, default)
        
    def set_intermediate_result(self, key: str, value: Any) -> None:
        """设置中间结果"""
        self.intermediate_results[key] = value
        self.updated_at = datetime.now().isoformat()
        
    def get_intermediate_result(self, key: str, default: Any = None) -> Any:
        """获取中间结果"""
        return self.intermediate_results.get(key, default)
        
    def set_final_result(self, result: Dict[str, Any]) -> None:
        """设置最终结果"""
        self.final_result = result
        self.updated_at = datetime.now().isoformat()
        
    def has_errors(self) -> bool:
        """检查是否有错误"""
        return len(self.errors) > 0
        
    def get_latest_error(self) -> Optional[Dict[str, Any]]:
        """获取最新的错误"""
        return self.errors[-1] if self.errors else None
        
    def get_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            "trace_id": self.trace_id,
            "user_id": self.user_id,
            "agent_id": self.agent_id,
            "current_step": self.current_step,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "has_errors": self.has_errors(),
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "has_final_result": self.final_result is not None,
            "context_keys": list(self.context.keys()),
            "intermediate_result_keys": list(self.intermediate_results.keys())
        }


def create_base_state(
    trace_id: str,
    user_id: str,
    agent_id: str,
    original_query: str,
    **kwargs
) -> BaseAgentState:
    """
    创建基础Agent状态
    
    Args:
        trace_id: 追踪ID
        user_id: 用户ID
        agent_id: Agent实例ID
        original_query: 原始查询
        **kwargs: 其他状态字段
        
    Returns:
        BaseAgentState: 基础状态对象
    """
    now = datetime.now().isoformat()
    
    state = BaseAgentState(
        trace_id=trace_id,
        user_id=user_id,
        agent_id=agent_id,
        original_query=original_query,
        current_step="init",
        created_at=now,
        updated_at=now,
        context={},
        intermediate_results={},
        final_result=None,
        errors=[],
        warnings=[],
        metadata={}
    )
    
    # 添加额外的字段
    state.update(kwargs)
    
    return state


def update_state_step(state: BaseAgentState, step: str) -> BaseAgentState:
    """
    更新状态的当前步骤
    
    Args:
        state: 当前状态
        step: 新的步骤名称
        
    Returns:
        BaseAgentState: 更新后的状态
    """
    updated_state = state.copy()
    updated_state["current_step"] = step
    updated_state["updated_at"] = datetime.now().isoformat()
    return updated_state


def add_state_error(
    state: BaseAgentState, 
    error: str, 
    step: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> BaseAgentState:
    """
    向状态添加错误信息
    
    Args:
        state: 当前状态
        error: 错误信息
        step: 发生错误的步骤
        details: 错误详情
        
    Returns:
        BaseAgentState: 更新后的状态
    """
    updated_state = state.copy()
    
    error_info = {
        "error": error,
        "step": step or state.get("current_step", "unknown"),
        "timestamp": datetime.now().isoformat(),
        "details": details or {}
    }
    
    if "errors" not in updated_state:
        updated_state["errors"] = []
    
    updated_state["errors"].append(error_info)
    updated_state["updated_at"] = datetime.now().isoformat()
    
    return updated_state
