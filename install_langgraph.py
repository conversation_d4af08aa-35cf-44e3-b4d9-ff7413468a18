"""
安装LangGraph和相关依赖的脚本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"❌ {description} 失败")
            print(f"错误: {result.stderr.strip()}")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始安装LangGraph和相关依赖")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 要安装的包
    packages = [
        "langgraph",
        "langchain",
        "langchain-core", 
        "pydantic>=2.0.0"
    ]
    
    success_count = 0
    
    for package in packages:
        success = run_command(
            f"pip install {package}",
            f"安装 {package}"
        )
        if success:
            success_count += 1
    
    print("\n" + "=" * 50)
    print("📊 安装结果汇总")
    print("=" * 50)
    
    print(f"成功安装: {success_count}/{len(packages)} 个包")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装成功！")
        print("\n下一步: 运行测试脚本")
        print("python test_langgraph_agent.py")
    else:
        print("⚠️ 部分依赖安装失败，请检查错误信息")
    
    # 验证安装
    print("\n🔍 验证安装结果...")
    
    try:
        import langgraph
        print("✅ langgraph 导入成功")
        print(f"   版本: {getattr(langgraph, '__version__', '未知')}")
    except ImportError as e:
        print(f"❌ langgraph 导入失败: {e}")
    
    try:
        import langchain
        print("✅ langchain 导入成功")
        print(f"   版本: {getattr(langchain, '__version__', '未知')}")
    except ImportError as e:
        print(f"❌ langchain 导入失败: {e}")

if __name__ == "__main__":
    main()
