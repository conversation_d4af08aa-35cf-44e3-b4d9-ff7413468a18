"""
LangGraph旅行规划Agent测试脚本

测试新重构的LangGraphTravelPlannerAgent的功能
"""

import asyncio
import sys
import traceback
from datetime import datetime
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, 'src')

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖安装情况...")
    
    missing_deps = []
    
    # 检查基础依赖
    try:
        import pydantic
        print("✅ pydantic 已安装")
    except ImportError:
        missing_deps.append("pydantic")
        
    try:
        import asyncio
        print("✅ asyncio 可用")
    except ImportError:
        missing_deps.append("asyncio")
        
    # 检查LangGraph
    try:
        import langgraph
        print("✅ langgraph 已安装")
    except ImportError:
        print("❌ langgraph 未安装")
        missing_deps.append("langgraph")
        
    # 检查项目模块
    try:
        from src.agents.travel_planner import LangGraphTravelPlannerAgent
        print("✅ LangGraphTravelPlannerAgent 可导入")
    except ImportError as e:
        print(f"❌ LangGraphTravelPlannerAgent 导入失败: {e}")
        
    try:
        from src.models.travel_models import TravelPlanRequest
        print("✅ TravelPlanRequest 可导入")
    except ImportError as e:
        print(f"❌ TravelPlanRequest 导入失败: {e}")
        
    if missing_deps:
        print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install " + " ".join(missing_deps))
        return False
    else:
        print("\n✅ 所有依赖检查通过")
        return True


async def test_agent_creation():
    """测试Agent创建"""
    print("\n🧪 测试1: Agent创建")
    
    try:
        from src.agents.travel_planner import LangGraphTravelPlannerAgent
        
        # 创建Agent实例
        agent = LangGraphTravelPlannerAgent()
        
        print(f"✅ Agent创建成功")
        print(f"   Agent ID: {agent.agent_id}")
        print(f"   Agent类型: {agent.__class__.__name__}")
        
        return agent
        
    except Exception as e:
        print(f"❌ Agent创建失败: {e}")
        traceback.print_exc()
        return None


async def test_health_check(agent):
    """测试健康检查"""
    print("\n🧪 测试2: 健康检查")
    
    try:
        health_status = await agent.health_check()
        
        print("✅ 健康检查完成")
        print(f"   状态: {health_status.get('status')}")
        print(f"   Agent类型: {health_status.get('agent_type')}")
        print(f"   LangGraph工作流: {health_status.get('langgraph_workflow')}")
        print(f"   Redis服务: {health_status.get('redis_service')}")
        
        return health_status.get('status') == 'healthy'
        
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        traceback.print_exc()
        return False


async def test_agent_info(agent):
    """测试Agent信息获取"""
    print("\n🧪 测试3: Agent信息")
    
    try:
        agent_info = agent.get_agent_info()
        
        print("✅ Agent信息获取成功")
        print(f"   Agent ID: {agent_info.get('agent_id')}")
        print(f"   框架: {agent_info.get('framework')}")
        print(f"   工作流类型: {agent_info.get('workflow_type')}")
        print(f"   能力: {len(agent_info.get('capabilities', []))}个")
        
        capabilities = agent_info.get('capabilities', [])
        if capabilities:
            print("   支持的能力:")
            for cap in capabilities:
                print(f"     - {cap}")
                
        return True
        
    except Exception as e:
        print(f"❌ Agent信息获取失败: {e}")
        traceback.print_exc()
        return False


async def test_request_validation(agent):
    """测试请求验证"""
    print("\n🧪 测试4: 请求验证")
    
    try:
        from src.models.travel_models import TravelPlanRequest
        
        # 创建有效请求
        valid_request = TravelPlanRequest(
            trace_id="test_trace_001",
            user_id="test_user_001", 
            query="我想去北京旅游3天"
        )
        
        # 测试有效请求
        is_valid = agent.validate_request(valid_request)
        print(f"✅ 有效请求验证: {is_valid}")
        
        # 测试无效请求
        is_invalid = agent.validate_request("invalid_request")
        print(f"✅ 无效请求验证: {not is_invalid}")
        
        return is_valid and not is_invalid
        
    except Exception as e:
        print(f"❌ 请求验证测试失败: {e}")
        traceback.print_exc()
        return False


async def test_core_modules():
    """测试核心模块导入"""
    print("\n🧪 测试5: 核心模块")
    
    try:
        from src.agents.travel_planner.core import IntentProcessor, POIAnalyzer, ItineraryComposer
        
        print("✅ 核心模块导入成功")
        
        # 测试模块创建
        intent_processor = IntentProcessor()
        poi_analyzer = POIAnalyzer()
        itinerary_composer = ItineraryComposer()
        
        print("✅ 核心模块实例化成功")
        print(f"   IntentProcessor: {intent_processor.__class__.__name__}")
        print(f"   POIAnalyzer: {poi_analyzer.__class__.__name__}")
        print(f"   ItineraryComposer: {itinerary_composer.__class__.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块测试失败: {e}")
        traceback.print_exc()
        return False


async def test_workflow_components():
    """测试工作流组件"""
    print("\n🧪 测试6: 工作流组件")
    
    try:
        from src.agents.travel_planner.workflow import (
            TravelPlannerState, 
            create_travel_planner_state,
            TravelPlannerNodes,
            TravelPlannerWorkflow
        )
        
        print("✅ 工作流组件导入成功")
        
        # 测试状态创建
        state = create_travel_planner_state(
            trace_id="test_trace",
            user_id="test_user",
            agent_id="test_agent",
            original_query="测试查询"
        )
        
        print("✅ 状态创建成功")
        print(f"   状态类型: {type(state)}")
        print(f"   Trace ID: {state.get('trace_id')}")
        
        # 测试节点创建
        nodes = TravelPlannerNodes()
        print("✅ 节点创建成功")
        
        # 测试工作流创建
        workflow = TravelPlannerWorkflow()
        print("✅ 工作流创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流组件测试失败: {e}")
        traceback.print_exc()
        return False


async def test_simple_workflow(agent):
    """测试简单工作流执行"""
    print("\n🧪 测试7: 简单工作流执行")
    
    try:
        from src.models.travel_models import TravelPlanRequest
        
        # 创建测试请求
        request = TravelPlanRequest(
            trace_id=f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            user_id="test_user_001",
            query="我想去杭州旅游2天，喜欢自然风景和美食"
        )
        
        print(f"✅ 测试请求创建成功")
        print(f"   Trace ID: {request.trace_id}")
        print(f"   查询: {request.query}")
        
        # 执行工作流（只收集前几个事件）
        event_count = 0
        max_events = 5  # 限制事件数量避免长时间运行
        
        print("🚀 开始执行工作流...")
        
        async for event in agent.process_request(request):
            event_count += 1
            
            print(f"📨 事件 {event_count}: {event.get('event_type', 'unknown')}")
            
            if event.get('step'):
                print(f"   步骤: {event.get('step')}")
                
            if event.get('progress') is not None:
                print(f"   进度: {event.get('progress')}%")
                
            if event.get('error'):
                print(f"   错误: {event.get('error')}")
                
            # 限制事件数量
            if event_count >= max_events:
                print(f"⏹️ 达到最大事件数量限制 ({max_events})，停止测试")
                break
                
        print(f"✅ 工作流测试完成，共处理 {event_count} 个事件")
        return True
        
    except Exception as e:
        print(f"❌ 工作流执行测试失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 LangGraph旅行规划Agent测试开始")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺失的依赖")
        return
    
    # 测试结果统计
    test_results = []
    
    # 测试1: Agent创建
    agent = await test_agent_creation()
    test_results.append(agent is not None)
    
    if agent is None:
        print("\n❌ Agent创建失败，无法继续后续测试")
        return
    
    # 测试2: 健康检查
    health_ok = await test_health_check(agent)
    test_results.append(health_ok)
    
    # 测试3: Agent信息
    info_ok = await test_agent_info(agent)
    test_results.append(info_ok)
    
    # 测试4: 请求验证
    validation_ok = await test_request_validation(agent)
    test_results.append(validation_ok)
    
    # 测试5: 核心模块
    modules_ok = await test_core_modules()
    test_results.append(modules_ok)
    
    # 测试6: 工作流组件
    workflow_ok = await test_workflow_components()
    test_results.append(workflow_ok)
    
    # 测试7: 简单工作流（如果前面的测试都通过）
    if all(test_results):
        workflow_exec_ok = await test_simple_workflow(agent)
        test_results.append(workflow_exec_ok)
    else:
        print("\n⚠️ 跳过工作流执行测试（前置测试未全部通过）")
        test_results.append(False)
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    test_names = [
        "Agent创建",
        "健康检查", 
        "Agent信息",
        "请求验证",
        "核心模块",
        "工作流组件",
        "工作流执行"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{len(test_results)} 测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！新架构工作正常。")
    elif passed >= len(test_results) - 2:
        print("⚠️ 大部分测试通过，架构基本可用，需要修复少量问题。")
    else:
        print("❌ 多个测试失败，需要进一步调试和修复。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        traceback.print_exc()
