"""
基于LangGraph的旅行规划Agent

使用LangGraph框架重构的模块化旅行规划Agent
"""

import uuid
from typing import Dict, Any, AsyncGenerator, Optional
from datetime import datetime

from src.agents.common.base import WorkflowAgent
from src.agents.common.workflow import EventEmitter, EventType
from src.agents.common.services import RedisService
from src.models.travel_models import TravelPlanRequest
from src.core.logger import get_logger

from .workflow.graph_builder import TravelPlannerWorkflow
from .workflow.state import TravelPlannerState

logger = get_logger(__name__)


class LangGraphTravelPlannerAgent(WorkflowAgent):
    """基于LangGraph的旅行规划Agent"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化LangGraph旅行规划Agent
        
        Args:
            config: Agent配置
        """
        super().__init__(config)
        self.logger = logger
        
        # 初始化服务
        self.redis_service = RedisService()
        
        # 初始化LangGraph工作流
        self.workflow = TravelPlannerWorkflow()
        
        self.logger.info(f"LangGraph旅行规划Agent初始化完成: {self.agent_id}")
        
    async def process_request(
        self, 
        request: TravelPlanRequest, 
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理旅行规划请求
        
        Args:
            request: 旅行规划请求
            **kwargs: 额外参数
            
        Yields:
            Dict[str, Any]: 流式事件数据
        """
        # 创建事件发射器
        event_emitter = EventEmitter(
            session_id=request.trace_id,
            agent_id=self.agent_id
        )
        
        try:
            # 初始化Redis任务状态
            await self._initialize_redis_task(request)
            
            # 发射开始事件
            await event_emitter.emit_progress(
                "开始处理旅行规划请求",
                0,
                "request_start"
            )
            
            # 执行LangGraph工作流
            async for workflow_event in self.workflow.execute_workflow(
                trace_id=request.trace_id,
                user_id=request.user_id,
                agent_id=self.agent_id,
                original_query=request.query,
                event_emitter=event_emitter,
                **kwargs
            ):
                # 转换工作流事件为标准事件格式
                standard_event = self._transform_workflow_event(workflow_event, request)
                yield standard_event
                
                # 更新Redis任务进度
                await self._update_redis_progress(request, workflow_event)
                
            # 发射完成事件
            await event_emitter.emit_progress(
                "旅行规划请求处理完成",
                100,
                "request_complete"
            )
            
        except Exception as e:
            self.logger.error(f"处理旅行规划请求失败: {str(e)}")
            
            # 发射错误事件
            await event_emitter.emit_error(f"处理请求失败: {str(e)}")
            
            yield {
                "event_type": "error",
                "error": str(e),
                "trace_id": request.trace_id,
                "agent_id": self.agent_id,
                "timestamp": datetime.now().isoformat()
            }
            
        finally:
            # 停止事件发射器
            event_emitter.stop()
            
    def validate_request(self, request: Any) -> bool:
        """
        验证旅行规划请求
        
        Args:
            request: 请求对象
            
        Returns:
            bool: 验证结果
        """
        if not isinstance(request, TravelPlanRequest):
            return False
            
        # 检查必要字段
        if not request.trace_id or not request.user_id or not request.query:
            return False
            
        return True
        
    async def _initialize_redis_task(self, request: TravelPlanRequest) -> None:
        """
        初始化Redis任务状态
        
        Args:
            request: 旅行规划请求
        """
        try:
            success = await self.redis_service.create_task(
                task_id=request.trace_id,
                user_id=request.user_id,
                original_query=request.query
            )
            
            if success:
                await self.redis_service.update_task_progress(
                    request.trace_id,
                    0.0,
                    "initialized"
                )
                
                self.logger.info(f"Redis任务初始化成功: {request.trace_id}")
            else:
                self.logger.warning(f"Redis任务初始化失败: {request.trace_id}")
                
        except Exception as e:
            self.logger.error(f"Redis任务初始化异常: {str(e)}")
            
    async def _update_redis_progress(
        self, 
        request: TravelPlanRequest, 
        workflow_event: Dict[str, Any]
    ) -> None:
        """
        更新Redis任务进度
        
        Args:
            request: 旅行规划请求
            workflow_event: 工作流事件
        """
        try:
            # 从工作流事件中提取进度信息
            progress = self._extract_progress_from_event(workflow_event)
            step = workflow_event.get("step", "unknown")
            
            if progress is not None:
                await self.redis_service.update_task_progress(
                    request.trace_id,
                    progress,
                    step
                )
                
                # 记录步骤日志
                await self.redis_service.log_step(
                    request.trace_id,
                    step,
                    {"event_data": workflow_event}
                )
                
        except Exception as e:
            self.logger.error(f"更新Redis进度失败: {str(e)}")
            
    def _transform_workflow_event(
        self, 
        workflow_event: Dict[str, Any], 
        request: TravelPlanRequest
    ) -> Dict[str, Any]:
        """
        转换工作流事件为标准事件格式
        
        Args:
            workflow_event: 工作流事件
            request: 原始请求
            
        Returns:
            Dict[str, Any]: 标准事件格式
        """
        return {
            "event_id": str(uuid.uuid4()),
            "event_type": workflow_event.get("event_type", "workflow_event"),
            "trace_id": request.trace_id,
            "user_id": request.user_id,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat(),
            "data": workflow_event,
            "step": workflow_event.get("step"),
            "progress": self._extract_progress_from_event(workflow_event)
        }
        
    def _extract_progress_from_event(self, event: Dict[str, Any]) -> Optional[float]:
        """
        从事件中提取进度信息
        
        Args:
            event: 事件数据
            
        Returns:
            Optional[float]: 进度百分比
        """
        # 尝试从不同字段提取进度信息
        progress = event.get("progress")
        if progress is not None:
            return float(progress)
            
        # 根据步骤名称估算进度
        step = event.get("step", "")
        step_progress_map = {
            "intent_understanding": 10.0,
            "personalization": 20.0,
            "poi_search": 40.0,
            "weather_search": 45.0,
            "poi_scoring": 60.0,
            "itinerary_orchestration": 80.0,
            "final_generation": 95.0,
            "memory_saving": 100.0
        }
        
        return step_progress_map.get(step)
        
    async def health_check(self) -> Dict[str, Any]:
        """
        Agent健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        base_health = await super().health_check()
        
        # 检查Redis服务
        redis_healthy = await self.redis_service.health_check()
        
        # 检查工作流状态
        workflow_healthy = self.workflow is not None
        
        health_status = {
            **base_health,
            "langgraph_workflow": workflow_healthy,
            "redis_service": redis_healthy,
            "agent_type": "LangGraphTravelPlannerAgent",
            "workflow_nodes": len(self.workflow.compiled_graph.graph.nodes) if workflow_healthy else 0
        }
        
        # 判断整体健康状态
        overall_healthy = all([
            base_health.get("status") == "healthy",
            workflow_healthy,
            redis_healthy
        ])
        
        health_status["status"] = "healthy" if overall_healthy else "unhealthy"
        
        return health_status
        
    def get_agent_info(self) -> Dict[str, Any]:
        """
        获取Agent信息
        
        Returns:
            Dict[str, Any]: Agent信息
        """
        base_info = super().get_agent_info()
        
        return {
            **base_info,
            "agent_type": "LangGraphTravelPlannerAgent",
            "framework": "LangGraph",
            "workflow_type": "state_graph",
            "capabilities": [
                "intent_understanding",
                "personalization",
                "poi_search",
                "weather_query",
                "poi_scoring",
                "itinerary_orchestration",
                "final_generation",
                "memory_saving"
            ],
            "supported_transport_modes": ["driving", "walking", "public_transport"]
        }
