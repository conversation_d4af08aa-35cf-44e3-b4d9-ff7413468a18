"""
旅行规划工作流节点定义

定义LangGraph状态图中的各个节点函数
"""

import asyncio
from typing import Dict, Any, List, Optional

from src.agents.common.services import LLMService, DatabaseService, RedisService
from src.agents.common.workflow import EventEmitter, EventType
from src.agents.travel_planner.core import IntentProcessor, POIAnalyzer, ItineraryComposer
from src.core.logger import get_logger

from .state import (
    TravelPlannerState, 
    update_travel_state_step,
    set_extracted_entities,
    set_user_profile,
    set_poi_results,
    set_orchestrated_itinerary,
    set_final_itinerary,
    add_tool_result
)

logger = get_logger(__name__)


class TravelPlannerNodes:
    """旅行规划工作流节点集合"""
    
    def __init__(self):
        """初始化节点处理器"""
        self.logger = logger
        self.llm_service = LLMService()
        self.db_service = DatabaseService()
        self.redis_service = RedisService()
        
        # 核心业务模块
        self.intent_processor = IntentProcessor(self.llm_service)
        self.poi_analyzer = POIAnalyzer(self.llm_service)
        self.itinerary_composer = ItineraryComposer(self.llm_service)
        
    # ==================== Phase 1: 意图理解与个性化融合 ====================
    
    async def intent_understanding_node(
        self, 
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        意图理解节点
        
        Args:
            state: 当前状态
            event_emitter: 事件发射器
            
        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "intent_understanding")
            
            if event_emitter:
                await event_emitter.emit_step_start(
                    "intent_understanding",
                    "开始理解用户意图"
                )
                
            # 提取旅行意图
            extracted_entities = await self.intent_processor.extract_travel_intent(
                state["original_query"]
            )
            
            # 更新状态
            state = set_extracted_entities(state, extracted_entities)
            
            if event_emitter:
                await event_emitter.emit_step_result(
                    "intent_understanding",
                    {"extracted_entities": extracted_entities},
                    f"成功提取意图信息：目的地={extracted_entities.get('destination', '未知')}"
                )
                
            self.logger.info(f"意图理解完成: {extracted_entities}")
            
        except Exception as e:
            self.logger.error(f"意图理解失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"意图理解失败: {str(e)}", "intent_understanding")
                
        return state
        
    async def personalization_node(
        self, 
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        个性化融合节点
        
        Args:
            state: 当前状态
            event_emitter: 事件发射器
            
        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "personalization")
            
            if event_emitter:
                await event_emitter.emit_step_start(
                    "personalization",
                    "获取用户画像和个性化信息"
                )
                
            # 获取用户画像
            user_profile = await self.db_service.get_user_profile(state["user_id"])
            
            if user_profile:
                state = set_user_profile(state, user_profile)
                
                # 使用用户画像增强意图信息
                enhanced_entities = await self.intent_processor.enhance_intent_with_profile(
                    state["extracted_entities"],
                    user_profile
                )
                
                state = set_extracted_entities(state, enhanced_entities)
                
                if event_emitter:
                    await event_emitter.emit_step_result(
                        "personalization",
                        {"user_profile_loaded": True},
                        "成功加载用户画像并增强意图信息"
                    )
            else:
                if event_emitter:
                    await event_emitter.emit_warning(
                        "未找到用户画像，使用默认配置",
                        "personalization"
                    )
                    
            self.logger.info(f"个性化融合完成: user_id={state['user_id']}")
            
        except Exception as e:
            self.logger.error(f"个性化融合失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"个性化融合失败: {str(e)}", "personalization")
                
        return state
        
    # ==================== Phase 2: 动态工具规划与并行执行 ====================
    
    async def poi_search_node(
        self, 
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        POI搜索节点
        
        Args:
            state: 当前状态
            event_emitter: 事件发射器
            
        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "poi_search")
            
            destination = state["extracted_entities"].get("destination")
            if not destination:
                if event_emitter:
                    await event_emitter.emit_warning(
                        "未找到目的地信息，跳过POI搜索",
                        "poi_search"
                    )
                return state
                
            if event_emitter:
                await event_emitter.emit_step_start(
                    "poi_search",
                    f"开始搜索{destination}的POI信息"
                )
                
            # 搜索POI
            poi_results = await self.poi_analyzer.search_pois_by_destination(destination)
            
            # 更新状态
            state = set_poi_results(state, poi_results)
            
            total_pois = sum(len(result.get('pois', [])) for result in poi_results.values())
            
            if event_emitter:
                await event_emitter.emit_step_result(
                    "poi_search",
                    {"poi_categories": len(poi_results), "total_pois": total_pois},
                    f"POI搜索完成，找到{total_pois}个POI"
                )
                
            self.logger.info(f"POI搜索完成: {destination}, 找到{total_pois}个POI")
            
        except Exception as e:
            self.logger.error(f"POI搜索失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"POI搜索失败: {str(e)}", "poi_search")
                
        return state
        
    async def weather_search_node(
        self, 
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        天气查询节点
        
        Args:
            state: 当前状态
            event_emitter: 事件发射器
            
        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "weather_search")
            
            destination = state["extracted_entities"].get("destination")
            if not destination:
                return state
                
            if event_emitter:
                await event_emitter.emit_step_start(
                    "weather_search",
                    f"查询{destination}的天气信息"
                )
                
            # TODO: 实现天气查询逻辑
            # 暂时使用模拟数据
            weather_data = [{
                "city": destination,
                "weather": "晴",
                "temperature": "22",
                "date": "今天"
            }]
            
            # 更新状态
            state = add_tool_result(state, "weather_forecast", weather_data)
            
            if event_emitter:
                await event_emitter.emit_step_result(
                    "weather_search",
                    {"weather_data": weather_data},
                    f"天气查询完成：{destination} - 晴，22°C"
                )
                
            self.logger.info(f"天气查询完成: {destination}")
            
        except Exception as e:
            self.logger.error(f"天气查询失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"天气查询失败: {str(e)}", "weather_search")
                
        return state

    # ==================== Phase 3: 数据综合与智能决策 ====================

    async def poi_scoring_node(
        self,
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        POI评分节点

        Args:
            state: 当前状态
            event_emitter: 事件发射器

        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "poi_scoring")

            if event_emitter:
                await event_emitter.emit_step_start(
                    "poi_scoring",
                    "开始对POI进行综合评分"
                )

            # 获取POI搜索结果
            poi_results = state.get("poi_search_results", {})
            if not poi_results:
                if event_emitter:
                    await event_emitter.emit_warning(
                        "没有POI搜索结果，跳过评分",
                        "poi_scoring"
                    )
                return state

            # 进行POI评分
            scored_pois = await self.poi_analyzer.score_and_rank_pois(
                poi_results,
                state.get("user_profile"),
                state.get("user_memories"),
                state.get("extracted_entities")
            )

            # 更新状态
            state = set_poi_results(state, poi_results, scored_pois)

            if event_emitter:
                await event_emitter.emit_step_result(
                    "poi_scoring",
                    {"scored_pois_count": len(scored_pois)},
                    f"POI评分完成，共评分{len(scored_pois)}个POI"
                )

            self.logger.info(f"POI评分完成: {len(scored_pois)}个POI")

        except Exception as e:
            self.logger.error(f"POI评分失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"POI评分失败: {str(e)}", "poi_scoring")

        return state

    async def itinerary_orchestration_node(
        self,
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        行程编排节点

        Args:
            state: 当前状态
            event_emitter: 事件发射器

        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "itinerary_orchestration")

            if event_emitter:
                await event_emitter.emit_step_start(
                    "itinerary_orchestration",
                    "开始智能行程编排"
                )

            # 获取评分后的POI
            scored_pois = state.get("scored_pois", [])
            if not scored_pois:
                if event_emitter:
                    await event_emitter.emit_warning(
                        "没有评分POI，跳过行程编排",
                        "itinerary_orchestration"
                    )
                return state

            # 进行行程编排
            orchestrated_data = await self.itinerary_composer.orchestrate_itinerary(
                scored_pois,
                state["extracted_entities"],
                state.get("user_profile"),
                state.get("tool_results", {}).get("weather_forecast")
            )

            # 更新状态
            state = set_orchestrated_itinerary(state, orchestrated_data)

            days = orchestrated_data.get("days", 1)
            total_pois = orchestrated_data.get("total_pois", 0)

            if event_emitter:
                await event_emitter.emit_step_result(
                    "itinerary_orchestration",
                    {"days": days, "total_pois": total_pois},
                    f"行程编排完成：{days}天行程，包含{total_pois}个POI"
                )

            self.logger.info(f"行程编排完成: {days}天行程")

        except Exception as e:
            self.logger.error(f"行程编排失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"行程编排失败: {str(e)}", "itinerary_orchestration")

        return state

    # ==================== Phase 4: 最终输出与记忆保存 ====================

    async def final_generation_node(
        self,
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        最终行程生成节点

        Args:
            state: 当前状态
            event_emitter: 事件发射器

        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "final_generation")

            if event_emitter:
                await event_emitter.emit_step_start(
                    "final_generation",
                    "生成最终旅行行程"
                )

            # 获取编排后的行程数据
            orchestrated_data = state.get("orchestrated_itinerary")
            if not orchestrated_data:
                if event_emitter:
                    await event_emitter.emit_warning(
                        "没有编排后的行程数据，生成基础行程",
                        "final_generation"
                    )

            # 生成最终行程
            final_itinerary = await self.itinerary_composer.generate_final_itinerary(
                orchestrated_data or {},
                state.get("route_results", {}),
                state["extracted_entities"],
                state.get("tool_results", {}).get("weather_forecast"),
                state.get("budget_estimation"),
                state["trace_id"],
                state["user_id"]
            )

            # 更新状态
            state = set_final_itinerary(state, final_itinerary.model_dump())

            if event_emitter:
                await event_emitter.emit_final_result(
                    final_itinerary.model_dump(),
                    f"最终行程生成完成：{final_itinerary.summary.title}"
                )

            self.logger.info(f"最终行程生成完成: {state['trace_id']}")

        except Exception as e:
            self.logger.error(f"最终行程生成失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"最终行程生成失败: {str(e)}", "final_generation")

        return state

    async def memory_saving_node(
        self,
        state: TravelPlannerState,
        event_emitter: Optional[EventEmitter] = None
    ) -> TravelPlannerState:
        """
        记忆保存节点

        Args:
            state: 当前状态
            event_emitter: 事件发射器

        Returns:
            TravelPlannerState: 更新后的状态
        """
        try:
            # 更新步骤
            state = update_travel_state_step(state, "memory_saving")

            if event_emitter:
                await event_emitter.emit_step_start(
                    "memory_saving",
                    "保存用户记忆和偏好"
                )

            # 提取用户偏好信息
            extracted_entities = state.get("extracted_entities", {})
            preferences = extracted_entities.get("preferences", [])

            if preferences:
                # 保存用户记忆
                memory_data = {
                    "memory_type": "travel_preference",
                    "content": {
                        "preferences": preferences,
                        "destination": extracted_entities.get("destination"),
                        "trip_duration": extracted_entities.get("days"),
                        "created_from_query": state["original_query"]
                    }
                }

                success = await self.db_service.save_user_memory(
                    state["user_id"],
                    memory_data
                )

                if success and event_emitter:
                    await event_emitter.emit_step_result(
                        "memory_saving",
                        {"memory_saved": True},
                        "用户偏好记忆保存成功"
                    )

            self.logger.info(f"记忆保存完成: user_id={state['user_id']}")

        except Exception as e:
            self.logger.error(f"记忆保存失败: {str(e)}")
            if event_emitter:
                await event_emitter.emit_error(f"记忆保存失败: {str(e)}", "memory_saving")

        return state
