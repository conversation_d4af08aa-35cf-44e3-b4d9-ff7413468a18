"""
POI分析器

负责POI搜索、评分、筛选和排序等核心功能
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from src.agents.common.services import LLMService
from src.tools.amap_mcp_client import get_amap_client
from src.prompts import get_travel_planner_prompts, PromptContext
from src.core.logger import get_logger

logger = get_logger(__name__)


class POIAnalyzer:
    """POI分析与评分处理器"""
    
    def __init__(self, llm_service: Optional[LLMService] = None):
        """
        初始化POI分析器
        
        Args:
            llm_service: LLM服务实例
        """
        self.logger = logger
        self.llm_service = llm_service or LLMService()
        self.prompts = get_travel_planner_prompts()
        
        # POI搜索类别配置
        self.poi_categories = [
            ("停车场", "停车场"),
            ("充电桩", "充电桩"), 
            ("美食", "美食"),
            ("景点", "景点"),
            ("酒店", "酒店")
        ]
        
    async def search_pois_by_destination(
        self, 
        destination: str,
        categories: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        按目的地搜索POI
        
        Args:
            destination: 目的地城市
            categories: 搜索类别列表，如果为None则使用默认类别
            
        Returns:
            Dict[str, Any]: 搜索结果，键为类别，值为POI列表
        """
        if not destination:
            self.logger.warning("目的地为空，无法搜索POI")
            return {}
            
        search_categories = categories or [cat[0] for cat in self.poi_categories]
        results = {}
        
        try:
            amap_client = await get_amap_client()
            
            # 并行搜索各类别POI
            search_tasks = []
            for category in search_categories:
                task = self._search_single_category(amap_client, destination, category)
                search_tasks.append((category, task))
                
            # 等待所有搜索完成
            for category, task in search_tasks:
                try:
                    search_result = await task
                    results[f"poi_{category}"] = search_result
                    
                    # 获取前3个POI的详细信息
                    await self._fetch_poi_details(amap_client, search_result, results)
                    
                    self.logger.info(f"POI搜索完成: {category}, 找到{len(search_result.get('pois', []))}个结果")
                    
                except Exception as e:
                    self.logger.error(f"搜索{category}POI失败: {str(e)}")
                    results[f"poi_{category}"] = {"pois": [], "status": "0", "error": str(e)}
                    
        except Exception as e:
            self.logger.error(f"POI搜索初始化失败: {str(e)}")
            
        return results
        
    async def _search_single_category(
        self, 
        amap_client, 
        destination: str, 
        category: str
    ) -> Dict[str, Any]:
        """
        搜索单个类别的POI
        
        Args:
            amap_client: 高德地图客户端
            destination: 目的地
            category: POI类别
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            search_result = await amap_client.maps_text_search(
                keywords=category,
                city=destination,
                offset=10
            )
            return search_result
            
        except Exception as e:
            self.logger.error(f"搜索{category}POI失败: {str(e)}")
            # 返回降级数据
            return self._get_fallback_poi_data(category)
            
    async def _fetch_poi_details(
        self, 
        amap_client, 
        search_result: Dict[str, Any], 
        results: Dict[str, Any]
    ) -> None:
        """
        获取POI详细信息
        
        Args:
            amap_client: 高德地图客户端
            search_result: 搜索结果
            results: 结果字典，用于存储详细信息
        """
        pois = search_result.get('pois', [])[:3]  # 只获取前3个POI的详情
        
        for poi in pois:
            poi_id = poi.get('id')
            if poi_id:
                try:
                    detail_result = await amap_client.maps_search_detail(id=poi_id)
                    results[f"poi_detail_{poi_id}"] = detail_result
                    
                except Exception as e:
                    self.logger.error(f"获取POI详情失败 {poi_id}: {str(e)}")
                    
    def _get_fallback_poi_data(self, category: str) -> Dict[str, Any]:
        """
        获取降级POI数据
        
        Args:
            category: POI类别
            
        Returns:
            Dict[str, Any]: 降级数据
        """
        return {
            'pois': [{
                'id': f'fallback_{category}_001',
                'name': f'{category}推荐地点',
                'type': '景点景区' if category == '景点' else category,
                'location': '116.407526,39.90403',
                'address': '暂无具体地址',
                'tel': '',
                'rating': '4.5',
                'tag': category,
                'indoor_map': '0',
                'photos': []
            }],
            'status': '1',
            'info': 'OK',
            'count': '1',
            '_fallback': True
        }
        
    async def score_and_rank_pois(
        self,
        poi_results: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]] = None,
        user_memories: Optional[List[Dict[str, Any]]] = None,
        extracted_entities: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        对POI进行综合评分和排序
        
        Args:
            poi_results: POI搜索结果
            user_profile: 用户画像
            user_memories: 用户记忆
            extracted_entities: 提取的实体信息
            
        Returns:
            List[Dict[str, Any]]: 评分排序后的POI列表
        """
        all_pois = []
        
        # 收集所有POI并进行基础评分
        for key, value in poi_results.items():
            if key.startswith("poi_") and isinstance(value, dict):
                pois = value.get('pois', [])
                category = key.replace('poi_', '')
                
                for poi in pois:
                    poi_copy = poi.copy()
                    poi_copy['category'] = category
                    poi_copy['score'] = self._calculate_poi_score(
                        poi_copy, user_profile, user_memories
                    )
                    all_pois.append(poi_copy)
                    
        # 如果有足够的POI，使用LLM进行智能评分
        if len(all_pois) > 5:
            try:
                enhanced_pois = await self._llm_enhanced_scoring(
                    all_pois[:20],  # 限制数量避免Prompt过长
                    user_profile,
                    user_memories,
                    extracted_entities
                )
                all_pois = enhanced_pois
                
            except Exception as e:
                self.logger.error(f"LLM增强评分失败: {str(e)}")
                
        # 按评分排序
        sorted_pois = sorted(all_pois, key=lambda x: x.get('score', 0), reverse=True)
        
        self.logger.info(f"POI评分完成，共{len(sorted_pois)}个POI，最高分: {sorted_pois[0].get('score', 0) if sorted_pois else 0}")
        
        return sorted_pois
        
    def _calculate_poi_score(
        self, 
        poi: Dict[str, Any], 
        user_profile: Optional[Dict[str, Any]] = None,
        user_memories: Optional[List[Dict[str, Any]]] = None
    ) -> float:
        """
        基于用户画像和记忆计算POI综合得分
        
        Args:
            poi: POI信息
            user_profile: 用户画像
            user_memories: 用户记忆
            
        Returns:
            float: 综合得分
        """
        score = 0.0
        
        # 基础评分权重 (30%)
        rating = poi.get('rating', '0')
        try:
            rating_float = float(rating) if rating else 0
            score += rating_float * 0.3
        except (ValueError, TypeError):
            pass
            
        # 用户画像偏好匹配 (40%)
        if user_profile:
            profile_score = self._calculate_profile_match_score(poi, user_profile)
            score += profile_score * 0.4
            
        # 用户记忆匹配 (20%)
        if user_memories:
            memory_score = self._calculate_memory_match_score(poi, user_memories)
            score += memory_score * 0.2
            
        # 类别权重调整 (10%)
        category = poi.get('category', '')
        category_weights = {
            '景点': 0.5,
            '美食': 0.3,
            '酒店': 0.2,
            '停车场': 0.1,
            '充电桩': 0.1
        }
        score += category_weights.get(category, 0.1)
        
        return min(score, 5.0)  # 限制最高分数
        
    def _calculate_profile_match_score(
        self, 
        poi: Dict[str, Any], 
        user_profile: Dict[str, Any]
    ) -> float:
        """
        计算POI与用户画像的匹配分数
        
        Args:
            poi: POI信息
            user_profile: 用户画像
            
        Returns:
            float: 匹配分数
        """
        match_score = 0.0
        
        # 检查兴趣偏好匹配
        travel_preferences = user_profile.get('travel_preferences', [])
        poi_type = poi.get('type', '').lower()
        poi_name = poi.get('name', '').lower()
        
        for preference in travel_preferences:
            if preference.lower() in poi_type or preference.lower() in poi_name:
                match_score += 1.0
                
        # 检查预算偏好匹配
        budget_pref = user_profile.get('budget_preference', '')
        rating = poi.get('rating', '0')
        
        try:
            rating_float = float(rating) if rating else 0
            
            if budget_pref == '经济型':
                # 经济型用户偏好评分相对较低但性价比高的地方
                if 3.5 <= rating_float <= 4.2:
                    match_score += 0.5
            elif budget_pref == '豪华型':
                # 豪华型用户偏好高评分场所
                if rating_float >= 4.5:
                    match_score += 1.0
                    
        except (ValueError, TypeError):
            pass
            
        return min(match_score, 5.0)  # 限制最高分数
        
    def _calculate_memory_match_score(
        self, 
        poi: Dict[str, Any], 
        user_memories: List[Dict[str, Any]]
    ) -> float:
        """
        计算POI与用户记忆的匹配分数
        
        Args:
            poi: POI信息
            user_memories: 用户记忆列表
            
        Returns:
            float: 匹配分数
        """
        match_score = 0.0
        
        poi_name = poi.get('name', '').lower()
        poi_type = poi.get('type', '').lower()
        
        for memory in user_memories:
            memory_content = str(memory.get('content', '')).lower()
            
            # 检查POI名称或类型是否在记忆中出现
            if poi_name in memory_content or poi_type in memory_content:
                match_score += 0.5
                
            # 检查记忆类型匹配
            memory_type = memory.get('memory_type', '')
            if memory_type == 'travel_preference':
                # 旅行偏好记忆权重更高
                match_score += 0.3
                
        return min(match_score, 3.0)  # 限制最高分数

    async def _llm_enhanced_scoring(
        self,
        pois: List[Dict[str, Any]],
        user_profile: Optional[Dict[str, Any]] = None,
        user_memories: Optional[List[Dict[str, Any]]] = None,
        extracted_entities: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        使用LLM进行增强评分

        Args:
            pois: POI列表
            user_profile: 用户画像
            user_memories: 用户记忆
            extracted_entities: 提取的实体信息

        Returns:
            List[Dict[str, Any]]: 增强评分后的POI列表
        """
        try:
            # 构建上下文
            context = PromptContext(
                user_id="temp_user",
                trace_id="temp_trace",
                extracted_entities=extracted_entities or {},
                tool_results={"pois": pois},
                user_profile=user_profile,
                user_memories=user_memories
            )

            # 生成POI评分Prompt
            scoring_prompt = self.prompts.render_poi_scoring_prompt(
                context=context,
                poi_candidates=pois
            )

            self.logger.info("使用LLM进行POI智能评分")

            # 调用推理模型进行智能评分
            response = await self.llm_service.structured_generation(
                prompt=scoring_prompt,
                schema=self._get_poi_scoring_schema(),
                role="reasoning",
                temperature=0.3
            )

            if response.get("parse_success"):
                scored_pois_data = response["parsed_json"]

                # 更新POI评分
                poi_scores = scored_pois_data.get("poi_scores", [])
                for poi_score in poi_scores:
                    poi_id = poi_score.get("poi_id")
                    new_score = poi_score.get("score", 0)

                    # 找到对应的POI并更新评分
                    for poi in pois:
                        if poi.get("id") == poi_id:
                            poi["llm_score"] = new_score
                            poi["score"] = (poi.get("score", 0) + new_score) / 2  # 平均分
                            break

                self.logger.info("LLM增强评分完成")

            else:
                self.logger.warning(f"LLM评分JSON解析失败: {response.get('parse_error')}")

        except Exception as e:
            self.logger.error(f"LLM增强评分失败: {str(e)}")

        return pois

    def _get_poi_scoring_schema(self) -> Dict[str, Any]:
        """获取POI评分的JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "poi_scores": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "poi_id": {"type": "string"},
                            "score": {"type": "number", "minimum": 0, "maximum": 5},
                            "reason": {"type": "string"}
                        },
                        "required": ["poi_id", "score"]
                    }
                }
            },
            "required": ["poi_scores"]
        }

    async def filter_pois_by_constraints(
        self,
        pois: List[Dict[str, Any]],
        constraints: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        根据约束条件筛选POI

        Args:
            pois: POI列表
            constraints: 约束条件

        Returns:
            List[Dict[str, Any]]: 筛选后的POI列表
        """
        filtered_pois = []

        min_rating = constraints.get("min_rating", 0)
        max_distance = constraints.get("max_distance")  # 单位：米
        required_categories = constraints.get("categories", [])
        exclude_categories = constraints.get("exclude_categories", [])

        for poi in pois:
            # 评分筛选
            rating = poi.get('rating', '0')
            try:
                rating_float = float(rating) if rating else 0
                if rating_float < min_rating:
                    continue
            except (ValueError, TypeError):
                continue

            # 类别筛选
            category = poi.get('category', '')
            if required_categories and category not in required_categories:
                continue

            if category in exclude_categories:
                continue

            # 距离筛选（如果提供了距离信息）
            if max_distance and 'distance' in poi:
                try:
                    distance = float(poi['distance'])
                    if distance > max_distance:
                        continue
                except (ValueError, TypeError):
                    pass

            filtered_pois.append(poi)

        self.logger.info(f"POI筛选完成: {len(pois)} -> {len(filtered_pois)}")
        return filtered_pois

    def get_pois_by_category(
        self,
        poi_results: Dict[str, Any],
        category: str
    ) -> List[Dict[str, Any]]:
        """
        按类别获取POI

        Args:
            poi_results: POI搜索结果
            category: 类别名称

        Returns:
            List[Dict[str, Any]]: 指定类别的POI列表
        """
        key = f"poi_{category}"
        if key in poi_results:
            return poi_results[key].get('pois', [])
        return []

    def get_top_pois_by_category(
        self,
        scored_pois: List[Dict[str, Any]],
        category: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        获取指定类别的高分POI

        Args:
            scored_pois: 已评分的POI列表
            category: 类别名称
            limit: 返回数量限制

        Returns:
            List[Dict[str, Any]]: 高分POI列表
        """
        category_pois = [poi for poi in scored_pois if poi.get('category') == category]
        category_pois.sort(key=lambda x: x.get('score', 0), reverse=True)
        return category_pois[:limit]
