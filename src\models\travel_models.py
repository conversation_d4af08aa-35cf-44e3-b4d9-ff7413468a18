"""
旅行规划相关的数据模型

整合旅行规划Agent所需的所有数据模型
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

# 从现有模块导入
from .travel_planner import TravelPlanRequest, EventType, TaskStatus
from .itinerary.itinerary_models import POILocation


class Location(BaseModel):
    """位置信息"""
    longitude: float = Field(..., description="经度")
    latitude: float = Field(..., description="纬度")


class POI(BaseModel):
    """兴趣点模型"""
    id: str = Field(..., description="POI ID")
    name: str = Field(..., description="名称")
    type: str = Field(..., description="类型")
    location: Optional[Location] = Field(None, description="位置")
    address: str = Field(default="", description="地址")
    tel: str = Field(default="", description="电话")
    rating: str = Field(default="", description="评分")
    tag: str = Field(default="", description="标签")
    indoor_map: str = Field(default="0", description="室内地图")
    photos: List[str] = Field(default_factory=list, description="照片列表")


class DailyPlan(BaseModel):
    """每日行程计划"""
    day: int = Field(..., description="第几天")
    theme: str = Field(..., description="主题")
    pois: List[POI] = Field(default_factory=list, description="POI列表")


class TripSummary(BaseModel):
    """行程摘要"""
    title: str = Field(..., description="标题")
    days: int = Field(..., description="天数")
    destination_city: str = Field(..., description="目的地城市")
    tags: List[str] = Field(default_factory=list, description="标签")
    description: str = Field(..., description="描述")


class TravelItinerary(BaseModel):
    """完整旅行行程"""
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    status: str = Field(..., description="状态")
    raw_user_query: str = Field(..., description="原始用户查询")
    summary: TripSummary = Field(..., description="行程摘要")
    weather_forecast: List[Dict[str, Any]] = Field(default_factory=list, description="天气预报")
    daily_plans: List[DailyPlan] = Field(default_factory=list, description="每日计划")
    budget_estimation: Optional[Dict[str, Any]] = Field(None, description="预算估算")


# 重新导出常用模型
__all__ = [
    "TravelPlanRequest",
    "EventType", 
    "TaskStatus",
    "Location",
    "POI",
    "DailyPlan", 
    "TripSummary",
    "TravelItinerary"
]
