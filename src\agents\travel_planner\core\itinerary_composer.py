"""
行程编排器

负责智能行程编排、时间安排、路线优化等功能
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from src.agents.common.services import LLMService
from src.tools.amap_mcp_client import get_amap_client
from src.prompts import get_travel_planner_prompts, PromptContext
from src.models.travel_models import DailyPlan, POI, Location, TripSummary, TravelItinerary
from src.core.logger import get_logger

logger = get_logger(__name__)


class ItineraryComposer:
    """行程编排与优化处理器"""
    
    def __init__(self, llm_service: Optional[LLMService] = None):
        """
        初始化行程编排器
        
        Args:
            llm_service: LLM服务实例
        """
        self.logger = logger
        self.llm_service = llm_service or LLMService()
        self.prompts = get_travel_planner_prompts()
        
    async def orchestrate_itinerary(
        self,
        scored_pois: List[Dict[str, Any]],
        extracted_entities: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]] = None,
        weather_data: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        智能行程编排
        
        Args:
            scored_pois: 已评分的POI列表
            extracted_entities: 提取的实体信息
            user_profile: 用户画像
            weather_data: 天气数据
            
        Returns:
            Dict[str, Any]: 编排后的行程数据
        """
        days = extracted_entities.get("days", 1)
        if days is None or not isinstance(days, int):
            days = 1
            
        destination = extracted_entities.get("destination", "未知")
        
        self.logger.info(f"开始智能行程编排: {destination}, {days}天")
        
        # 按类别分组POI
        categorized_pois = self._categorize_pois(scored_pois)
        
        # 为每天分配POI
        daily_plans = await self._allocate_pois_to_days(
            categorized_pois, 
            days, 
            extracted_entities,
            user_profile,
            weather_data
        )
        
        # 优化每日行程顺序
        optimized_plans = await self._optimize_daily_routes(daily_plans)
        
        # 构建编排结果
        orchestrated_data = {
            "destination": destination,
            "days": days,
            "daily_plans": [plan.model_dump() for plan in optimized_plans],
            "total_pois": len(scored_pois),
            "orchestrated_at": datetime.now().isoformat()
        }
        
        self.logger.info(f"行程编排完成: {days}天行程，包含{len(scored_pois)}个POI")
        
        return orchestrated_data
        
    def _categorize_pois(self, pois: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按类别分组POI
        
        Args:
            pois: POI列表
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 按类别分组的POI
        """
        categorized = {
            "景点": [],
            "美食": [],
            "酒店": [],
            "停车场": [],
            "充电桩": [],
            "其他": []
        }
        
        for poi in pois:
            category = poi.get("category", "其他")
            if category in categorized:
                categorized[category].append(poi)
            else:
                categorized["其他"].append(poi)
                
        return categorized
        
    async def _allocate_pois_to_days(
        self,
        categorized_pois: Dict[str, List[Dict[str, Any]]],
        days: int,
        extracted_entities: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]] = None,
        weather_data: Optional[List[Dict[str, Any]]] = None
    ) -> List[DailyPlan]:
        """
        为每天分配POI
        
        Args:
            categorized_pois: 按类别分组的POI
            days: 天数
            extracted_entities: 提取的实体信息
            user_profile: 用户画像
            weather_data: 天气数据
            
        Returns:
            List[DailyPlan]: 每日行程计划
        """
        daily_plans = []
        
        # 获取主要景点（按评分排序）
        attractions = sorted(
            categorized_pois.get("景点", []), 
            key=lambda x: x.get("score", 0), 
            reverse=True
        )
        
        # 获取美食推荐
        restaurants = sorted(
            categorized_pois.get("美食", []), 
            key=lambda x: x.get("score", 0), 
            reverse=True
        )
        
        # 为每天分配景点和美食
        attractions_per_day = max(1, len(attractions) // days)
        restaurants_per_day = max(1, len(restaurants) // days)
        
        for day in range(1, days + 1):
            # 计算当天的POI索引范围
            attraction_start = (day - 1) * attractions_per_day
            attraction_end = min(day * attractions_per_day, len(attractions))
            
            restaurant_start = (day - 1) * restaurants_per_day
            restaurant_end = min(day * restaurants_per_day, len(restaurants))
            
            # 获取当天的POI
            day_attractions = attractions[attraction_start:attraction_end]
            day_restaurants = restaurants[restaurant_start:restaurant_end]
            
            # 如果是最后一天，包含剩余的POI
            if day == days:
                day_attractions.extend(attractions[attraction_end:])
                day_restaurants.extend(restaurants[restaurant_end:])
                
            # 构建当天的POI列表
            day_pois = []
            
            # 添加景点POI
            for poi_data in day_attractions:
                poi = self._convert_to_poi_model(poi_data)
                if poi:
                    day_pois.append(poi)
                    
            # 添加美食POI
            for poi_data in day_restaurants:
                poi = self._convert_to_poi_model(poi_data)
                if poi:
                    day_pois.append(poi)
                    
            # 创建每日计划
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=day_pois
            )
            
            daily_plans.append(daily_plan)
            
        return daily_plans
        
    def _convert_to_poi_model(self, poi_data: Dict[str, Any]) -> Optional[POI]:
        """
        将POI数据转换为POI模型
        
        Args:
            poi_data: POI原始数据
            
        Returns:
            Optional[POI]: POI模型实例
        """
        try:
            # 解析位置信息
            location_str = poi_data.get("location", "")
            location = None
            
            if location_str and "," in location_str:
                try:
                    lng, lat = location_str.split(",")
                    location = Location(
                        longitude=float(lng),
                        latitude=float(lat)
                    )
                except (ValueError, IndexError):
                    pass
                    
            # 创建POI模型
            poi = POI(
                id=poi_data.get("id", ""),
                name=poi_data.get("name", ""),
                type=poi_data.get("type", ""),
                location=location,
                address=poi_data.get("address", ""),
                tel=poi_data.get("tel", ""),
                rating=poi_data.get("rating", ""),
                tag=poi_data.get("tag", ""),
                indoor_map=poi_data.get("indoor_map", "0"),
                photos=poi_data.get("photos", [])
            )
            
            return poi
            
        except Exception as e:
            self.logger.error(f"转换POI模型失败: {str(e)}")
            return None
            
    async def _optimize_daily_routes(self, daily_plans: List[DailyPlan]) -> List[DailyPlan]:
        """
        优化每日行程路线
        
        Args:
            daily_plans: 每日行程计划
            
        Returns:
            List[DailyPlan]: 优化后的每日行程计划
        """
        optimized_plans = []
        
        for daily_plan in daily_plans:
            if len(daily_plan.pois) <= 1:
                # 只有一个或没有POI，无需优化
                optimized_plans.append(daily_plan)
                continue
                
            try:
                # 使用TSP算法优化POI访问顺序
                optimized_pois = await self._optimize_poi_order(daily_plan.pois)
                
                # 创建优化后的每日计划
                optimized_plan = DailyPlan(
                    day=daily_plan.day,
                    theme=daily_plan.theme,
                    pois=optimized_pois
                )
                
                optimized_plans.append(optimized_plan)
                
            except Exception as e:
                self.logger.error(f"优化第{daily_plan.day}天路线失败: {str(e)}")
                # 使用原始计划
                optimized_plans.append(daily_plan)
                
        return optimized_plans
        
    async def _optimize_poi_order(self, pois: List[POI]) -> List[POI]:
        """
        优化POI访问顺序（简化版TSP）
        
        Args:
            pois: POI列表
            
        Returns:
            List[POI]: 优化后的POI列表
        """
        if len(pois) <= 2:
            return pois
            
        # 简化的贪心算法：从第一个POI开始，每次选择最近的未访问POI
        optimized = [pois[0]]
        remaining = pois[1:]
        
        while remaining:
            current_poi = optimized[-1]
            
            # 找到距离当前POI最近的POI
            nearest_poi = None
            min_distance = float('inf')
            
            for poi in remaining:
                distance = self._calculate_distance(current_poi, poi)
                if distance < min_distance:
                    min_distance = distance
                    nearest_poi = poi
                    
            if nearest_poi:
                optimized.append(nearest_poi)
                remaining.remove(nearest_poi)
            else:
                # 如果无法计算距离，按原顺序添加
                optimized.extend(remaining)
                break
                
        return optimized
        
    def _calculate_distance(self, poi1: POI, poi2: POI) -> float:
        """
        计算两个POI之间的直线距离
        
        Args:
            poi1: POI 1
            poi2: POI 2
            
        Returns:
            float: 距离（公里）
        """
        if not poi1.location or not poi2.location:
            return float('inf')
            
        # 使用简化的距离计算公式
        lat1, lng1 = poi1.location.latitude, poi1.location.longitude
        lat2, lng2 = poi2.location.latitude, poi2.location.longitude
        
        # 简化的距离计算（适用于短距离）
        lat_diff = abs(lat1 - lat2)
        lng_diff = abs(lng1 - lng2)
        
        # 粗略的距离估算（1度约等于111公里）
        distance = ((lat_diff ** 2 + lng_diff ** 2) ** 0.5) * 111
        
        return distance

    async def plan_routes_between_pois(
        self,
        daily_plans: List[DailyPlan],
        transport_mode: str = "driving"
    ) -> Dict[str, Any]:
        """
        规划POI之间的路线

        Args:
            daily_plans: 每日行程计划
            transport_mode: 交通方式 (driving/walking/public_transport)

        Returns:
            Dict[str, Any]: 路线规划结果
        """
        route_results = {}

        try:
            amap_client = await get_amap_client()

            for daily_plan in daily_plans:
                if len(daily_plan.pois) <= 1:
                    continue

                day_routes = []
                pois = daily_plan.pois

                for i in range(len(pois) - 1):
                    origin_poi = pois[i]
                    dest_poi = pois[i + 1]

                    if origin_poi.location and dest_poi.location:
                        try:
                            origin = f"{origin_poi.location.longitude},{origin_poi.location.latitude}"
                            destination = f"{dest_poi.location.longitude},{dest_poi.location.latitude}"

                            # 根据交通方式选择API
                            if transport_mode == "walking":
                                route_result = await amap_client.maps_direction_walking(
                                    origin=origin,
                                    destination=destination
                                )
                            elif transport_mode == "public_transport":
                                route_result = await amap_client.maps_direction_transit(
                                    origin=origin,
                                    destination=destination
                                )
                            else:  # driving
                                route_result = await amap_client.maps_direction_driving(
                                    origin=origin,
                                    destination=destination
                                )

                            route_info = {
                                "from_poi": origin_poi.name,
                                "to_poi": dest_poi.name,
                                "route_data": route_result,
                                "transport_mode": transport_mode
                            }

                            day_routes.append(route_info)

                        except Exception as e:
                            self.logger.error(f"路线规划失败: {origin_poi.name} -> {dest_poi.name}, 错误: {str(e)}")

                route_results[f"day_{daily_plan.day}_routes"] = day_routes

        except Exception as e:
            self.logger.error(f"路线规划初始化失败: {str(e)}")

        return route_results

    async def generate_final_itinerary(
        self,
        orchestrated_data: Dict[str, Any],
        route_results: Dict[str, Any],
        extracted_entities: Dict[str, Any],
        weather_forecast: Optional[List[Dict[str, Any]]] = None,
        budget_estimation: Optional[Dict[str, Any]] = None,
        trace_id: str = "",
        user_id: str = ""
    ) -> TravelItinerary:
        """
        生成最终行程

        Args:
            orchestrated_data: 编排后的行程数据
            route_results: 路线规划结果
            extracted_entities: 提取的实体信息
            weather_forecast: 天气预报
            budget_estimation: 预算估算
            trace_id: 追踪ID
            user_id: 用户ID

        Returns:
            TravelItinerary: 最终行程对象
        """
        try:
            destination = orchestrated_data.get("destination", "未知")
            days = orchestrated_data.get("days", 1)

            # 构建行程摘要
            summary = TripSummary(
                title=f"{destination}{days}日游",
                days=days,
                destination_city=destination,
                tags=["智能规划", extracted_entities.get("transport_mode", "driving")],
                description="AI智能规划的旅行行程"
            )

            # 构建每日计划
            daily_plans_data = orchestrated_data.get("daily_plans", [])
            daily_plans = []

            for plan_data in daily_plans_data:
                try:
                    # 重新构建DailyPlan对象
                    pois = []
                    for poi_data in plan_data.get("pois", []):
                        poi = self._dict_to_poi(poi_data)
                        if poi:
                            pois.append(poi)

                    daily_plan = DailyPlan(
                        day=plan_data.get("day", 1),
                        theme=plan_data.get("theme", ""),
                        pois=pois
                    )

                    daily_plans.append(daily_plan)

                except Exception as e:
                    self.logger.error(f"构建每日计划失败: {str(e)}")

            # 构建最终行程
            final_itinerary = TravelItinerary(
                trace_id=trace_id,
                user_id=user_id,
                status="completed",
                raw_user_query=extracted_entities.get("original_query", ""),
                summary=summary,
                weather_forecast=weather_forecast or [],
                daily_plans=daily_plans,
                budget_estimation=budget_estimation
            )

            self.logger.info(f"最终行程生成完成: {destination}, {days}天, {len(daily_plans)}个每日计划")

            return final_itinerary

        except Exception as e:
            self.logger.error(f"生成最终行程失败: {str(e)}")

            # 返回最小化的有效行程
            return self._create_fallback_itinerary(
                extracted_entities, trace_id, user_id
            )

    def _dict_to_poi(self, poi_data: Dict[str, Any]) -> Optional[POI]:
        """
        将字典数据转换为POI对象

        Args:
            poi_data: POI字典数据

        Returns:
            Optional[POI]: POI对象
        """
        try:
            # 处理位置信息
            location = None
            location_data = poi_data.get("location")

            if isinstance(location_data, dict):
                location = Location(
                    longitude=location_data.get("longitude", 0.0),
                    latitude=location_data.get("latitude", 0.0)
                )
            elif isinstance(location_data, str) and "," in location_data:
                try:
                    lng, lat = location_data.split(",")
                    location = Location(
                        longitude=float(lng),
                        latitude=float(lat)
                    )
                except (ValueError, IndexError):
                    pass

            poi = POI(
                id=poi_data.get("id", ""),
                name=poi_data.get("name", ""),
                type=poi_data.get("type", ""),
                location=location,
                address=poi_data.get("address", ""),
                tel=poi_data.get("tel", ""),
                rating=poi_data.get("rating", ""),
                tag=poi_data.get("tag", ""),
                indoor_map=poi_data.get("indoor_map", "0"),
                photos=poi_data.get("photos", [])
            )

            return poi

        except Exception as e:
            self.logger.error(f"转换POI对象失败: {str(e)}")
            return None

    def _create_fallback_itinerary(
        self,
        extracted_entities: Dict[str, Any],
        trace_id: str,
        user_id: str
    ) -> TravelItinerary:
        """
        创建降级行程

        Args:
            extracted_entities: 提取的实体信息
            trace_id: 追踪ID
            user_id: 用户ID

        Returns:
            TravelItinerary: 降级行程对象
        """
        destination = extracted_entities.get("destination", "未知目的地")
        days = extracted_entities.get("days", 1)

        # 创建基本摘要
        summary = TripSummary(
            title=f"{destination}{days}日游",
            days=days,
            destination_city=destination,
            tags=["基础规划"],
            description="基础旅行行程规划"
        )

        # 创建基本每日计划
        daily_plans = []
        for day in range(1, days + 1):
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=[]
            )
            daily_plans.append(daily_plan)

        return TravelItinerary(
            trace_id=trace_id,
            user_id=user_id,
            status="completed",
            raw_user_query=extracted_entities.get("original_query", ""),
            summary=summary,
            weather_forecast=[],
            daily_plans=daily_plans,
            budget_estimation=None
        )
