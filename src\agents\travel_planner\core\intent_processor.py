"""
意图处理器

负责理解用户的旅行意图，提取关键实体信息
"""

import json
import re
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.agents.common.services import LLMService
from src.prompts import get_travel_planner_prompts
from src.core.logger import get_logger

logger = get_logger(__name__)


class IntentProcessor:
    """意图理解与实体提取处理器"""
    
    def __init__(self, llm_service: Optional[LLMService] = None):
        """
        初始化意图处理器
        
        Args:
            llm_service: LLM服务实例
        """
        self.logger = logger
        self.llm_service = llm_service or LLMService()
        self.prompts = get_travel_planner_prompts()
        
    async def extract_travel_intent(
        self, 
        user_query: str, 
        user_profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        提取旅行意图和关键实体
        
        Args:
            user_query: 用户原始查询
            user_profile: 用户画像信息
            
        Returns:
            Dict[str, Any]: 提取的实体信息
        """
        try:
            # 获取当前时间信息
            current_datetime = self._get_current_datetime()
            
            # 使用Prompt管理系统生成提示词
            formatted_prompt = self.prompts.render_intent_extraction_prompt(
                user_query=user_query,
                current_datetime=current_datetime
            )
            
            self.logger.info(f"意图提取开始: {user_query[:50]}...")
            
            # 调用LLM进行结构化生成
            response = await self.llm_service.structured_generation(
                prompt=formatted_prompt,
                schema=self._get_intent_schema(),
                role="reasoning",
                temperature=0.1
            )
            
            if response.get("parse_success"):
                extracted_entities = response["parsed_json"]
                
                # 验证和清理提取的实体数据
                validated_entities = self._validate_extracted_entities(extracted_entities)
                
                self.logger.info(f"意图提取成功: {validated_entities}")
                return validated_entities
                
            else:
                self.logger.warning(f"JSON解析失败: {response.get('parse_error')}")
                return self._get_default_entities()
                
        except Exception as e:
            self.logger.error(f"意图提取失败: {str(e)}")
            return self._get_default_entities()
            
    def _get_current_datetime(self) -> str:
        """获取格式化的当前时间"""
        current_time = datetime.now()
        current_date = current_time.strftime("%Y年%m月%d日")
        current_weekday = current_time.strftime("%A")
        
        weekday_cn = {
            "Monday": "周一", "Tuesday": "周二", "Wednesday": "周三", 
            "Thursday": "周四", "Friday": "周五", "Saturday": "周六", "Sunday": "周日"
        }
        
        current_weekday_cn = weekday_cn.get(current_weekday, current_weekday)
        return f"{current_date} {current_weekday_cn}"
        
    def _get_intent_schema(self) -> Dict[str, Any]:
        """获取意图提取的JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "destination": {
                    "type": ["string", "null"],
                    "description": "目的地城市"
                },
                "origin": {
                    "type": ["string", "null"],
                    "description": "出发地城市"
                },
                "days": {
                    "type": ["integer", "null"],
                    "description": "出行天数",
                    "minimum": 1
                },
                "travel_time": {
                    "type": ["string", "null"],
                    "description": "出行时间"
                },
                "travelers": {
                    "type": ["string", "null"],
                    "description": "出行人数和类型"
                },
                "budget": {
                    "type": ["string", "null"],
                    "description": "预算范围"
                },
                "preferences": {
                    "type": ["array", "null"],
                    "items": {"type": "string"},
                    "description": "兴趣偏好列表"
                },
                "special_needs": {
                    "type": ["string", "null"],
                    "description": "特殊需求"
                },
                "transport_mode": {
                    "type": "string",
                    "enum": ["driving", "walking", "public_transport"],
                    "default": "driving",
                    "description": "出行方式"
                }
            },
            "required": ["transport_mode"]
        }
        
    def _validate_extracted_entities(self, entities: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和清理提取的实体数据
        
        Args:
            entities: 原始提取的实体
            
        Returns:
            Dict[str, Any]: 验证后的实体
        """
        validated = {}
        
        # 目的地验证
        destination = entities.get("destination")
        if destination and isinstance(destination, str) and destination.strip():
            validated["destination"] = destination.strip()
        else:
            validated["destination"] = None
            
        # 出发地验证
        origin = entities.get("origin")
        if origin and isinstance(origin, str) and origin.strip():
            validated["origin"] = origin.strip()
        else:
            validated["origin"] = None
            
        # 天数验证
        days = entities.get("days")
        if isinstance(days, int) and days > 0:
            validated["days"] = days
        elif isinstance(days, str) and days.isdigit():
            validated["days"] = int(days)
        else:
            validated["days"] = 1  # 默认1天
            
        # 出行时间验证
        travel_time = entities.get("travel_time")
        if travel_time and isinstance(travel_time, str):
            validated["travel_time"] = travel_time.strip()
        else:
            validated["travel_time"] = None
            
        # 出行人员验证
        travelers = entities.get("travelers")
        if travelers and isinstance(travelers, str):
            validated["travelers"] = travelers.strip()
        else:
            validated["travelers"] = None
            
        # 预算验证
        budget = entities.get("budget")
        if budget and isinstance(budget, str):
            validated["budget"] = budget.strip()
        else:
            validated["budget"] = None
            
        # 偏好验证
        preferences = entities.get("preferences")
        if isinstance(preferences, list):
            validated["preferences"] = [p for p in preferences if isinstance(p, str) and p.strip()]
        elif isinstance(preferences, str):
            # 如果是字符串，尝试分割
            validated["preferences"] = [p.strip() for p in preferences.split(",") if p.strip()]
        else:
            validated["preferences"] = []
            
        # 特殊需求验证
        special_needs = entities.get("special_needs")
        if special_needs and isinstance(special_needs, str):
            validated["special_needs"] = special_needs.strip()
        else:
            validated["special_needs"] = None
            
        # 出行方式验证
        transport_mode = entities.get("transport_mode", "driving")
        if transport_mode in ["driving", "walking", "public_transport"]:
            validated["transport_mode"] = transport_mode
        else:
            validated["transport_mode"] = "driving"
            
        return validated
        
    def _get_default_entities(self) -> Dict[str, Any]:
        """获取默认的实体信息"""
        return {
            "destination": None,
            "origin": None,
            "days": 1,
            "travel_time": None,
            "travelers": None,
            "budget": None,
            "preferences": [],
            "special_needs": None,
            "transport_mode": "driving"
        }
        
    def infer_destination_from_query(self, query: str) -> Optional[str]:
        """
        从用户查询中推断目的地
        
        Args:
            query: 用户查询
            
        Returns:
            Optional[str]: 推断的目的地
        """
        # 简单的地名提取逻辑
        city_pattern = r'([北京|上海|广州|深圳|杭州|南京|苏州|成都|重庆|西安|天津|青岛|大连|厦门|武汉|长沙|沈阳|哈尔滨|济南|昆明|贵阳|兰州|银川|西宁|拉萨|乌鲁木齐|呼和浩特|石家庄|太原|合肥|福州|南昌|郑州|长春|海口|三亚|桂林|丽江|九寨沟|黄山|泰山|华山|峨眉山|庐山|衡山|嵩山|恒山|五台山]|.*市|.*县|.*区)'
        
        match = re.search(city_pattern, query)
        if match:
            return match.group(1)
            
        return None
        
    def extract_preferences_from_history(self, itineraries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        从历史行程中提取偏好信息
        
        Args:
            itineraries: 历史行程列表
            
        Returns:
            Dict[str, Any]: 提取的偏好信息
        """
        preferences = {}
        
        destinations = []
        trip_durations = []
        
        for itinerary in itineraries:
            # 提取目的地偏好
            city_name = itinerary.get('city_name') or itinerary.get('destination')
            if city_name:
                destinations.append(city_name)
            
            # 提取行程天数偏好
            total_days = itinerary.get('total_days') or itinerary.get('days')
            if total_days and isinstance(total_days, (int, float)):
                trip_durations.append(int(total_days))
        
        # 设置偏好的目的地类型
        if destinations:
            preferences['preferred_destinations'] = list(set(destinations))[:5]
        
        # 设置偏好的行程天数
        if trip_durations:
            avg_duration = sum(trip_durations) / len(trip_durations)
            preferences['preferred_trip_duration'] = round(avg_duration)
        
        self.logger.debug(f"从历史行程提取偏好: {preferences}")
        return preferences
        
    async def enhance_intent_with_profile(
        self, 
        extracted_entities: Dict[str, Any],
        user_profile: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        使用用户画像增强意图信息
        
        Args:
            extracted_entities: 提取的实体信息
            user_profile: 用户画像
            
        Returns:
            Dict[str, Any]: 增强后的意图信息
        """
        enhanced_entities = extracted_entities.copy()
        
        # 如果没有提取到偏好，从用户画像中获取
        if not enhanced_entities.get("preferences"):
            profile_preferences = user_profile.get("travel_preferences", [])
            if profile_preferences:
                enhanced_entities["preferences"] = profile_preferences
                
        # 如果没有提取到预算，从用户画像中获取默认预算偏好
        if not enhanced_entities.get("budget"):
            default_budget = user_profile.get("default_budget")
            if default_budget:
                enhanced_entities["budget"] = default_budget
                
        # 如果没有提取到出行天数，从历史偏好中获取
        if not enhanced_entities.get("days") or enhanced_entities["days"] == 1:
            preferred_duration = user_profile.get("preferred_trip_duration")
            if preferred_duration and isinstance(preferred_duration, int):
                enhanced_entities["days"] = preferred_duration
                
        self.logger.debug(f"使用用户画像增强意图: {enhanced_entities}")
        return enhanced_entities
