"""
LLM服务封装

为Agent提供统一的LLM调用接口
"""

from typing import Dict, Any, Optional, List
import json

from src.core.llm_manager import LLMManager, get_default_manager
from src.core.logger import get_logger

logger = get_logger(__name__)


class LLMService:
    """Agent专用LLM服务封装"""
    
    def __init__(self, llm_manager: Optional[LLMManager] = None):
        """
        初始化LLM服务
        
        Args:
            llm_manager: LLM管理器实例，如果为None则使用默认管理器
        """
        self.logger = logger
        self.llm_manager = llm_manager or get_default_manager()
        
    # ==================== 基础聊天接口 ====================
    
    async def chat(
        self, 
        message: str, 
        role: str = "basic",
        system_message: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        发送聊天请求
        
        Args:
            message: 用户消息
            role: LLM角色 (reasoning/basic/map)
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            Dict[str, Any]: LLM响应
        """
        try:
            client = self.llm_manager.get_client(role)
            response = await client.chat(
                message=message,
                system_message=system_message,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            self.logger.debug(f"LLM聊天成功: role={role}, tokens={response.get('usage', {}).get('total_tokens', 0)}")
            return response
            
        except Exception as e:
            self.logger.error(f"LLM聊天失败: role={role}, 错误: {str(e)}")
            return {
                "content": "",
                "error": str(e),
                "usage": {"total_tokens": 0}
            }
            
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        role: str = "basic",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        OpenAI兼容的聊天完成接口
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            role: LLM角色
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: LLM响应
        """
        try:
            client = self.llm_manager.get_client(role)
            response = await client.chat_completion(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            self.logger.debug(f"LLM完成成功: role={role}, messages={len(messages)}")
            return response
            
        except Exception as e:
            self.logger.error(f"LLM完成失败: role={role}, 错误: {str(e)}")
            return {
                "choices": [{"message": {"content": ""}}],
                "error": str(e),
                "usage": {"total_tokens": 0}
            }
            
    # ==================== 高级接口 ====================
    
    async def reasoning_chat(
        self, 
        message: str, 
        context: Optional[Dict[str, Any]] = None,
        temperature: float = 0.3
    ) -> Dict[str, Any]:
        """
        推理型聊天，使用reasoning角色的LLM
        
        Args:
            message: 用户消息
            context: 上下文信息
            temperature: 温度参数（推理任务通常使用较低温度）
            
        Returns:
            Dict[str, Any]: LLM响应
        """
        # 构建系统消息
        system_message = "你是一个专业的AI助手，擅长逻辑推理和复杂问题分析。请仔细思考并给出详细的分析过程。"
        
        if context:
            system_message += f"\n\n上下文信息：\n{json.dumps(context, ensure_ascii=False, indent=2)}"
            
        return await self.chat(
            message=message,
            role="reasoning",
            system_message=system_message,
            temperature=temperature
        )
        
    async def basic_chat(
        self, 
        message: str, 
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        基础聊天，使用basic角色的LLM
        
        Args:
            message: 用户消息
            temperature: 温度参数
            
        Returns:
            Dict[str, Any]: LLM响应
        """
        return await self.chat(
            message=message,
            role="basic",
            temperature=temperature
        )
        
    async def structured_generation(
        self,
        prompt: str,
        schema: Dict[str, Any],
        role: str = "reasoning",
        temperature: float = 0.1
    ) -> Dict[str, Any]:
        """
        结构化生成，要求LLM按照指定schema返回JSON
        
        Args:
            prompt: 提示词
            schema: JSON Schema定义
            role: LLM角色
            temperature: 温度参数（结构化任务使用低温度）
            
        Returns:
            Dict[str, Any]: 包含解析后JSON的响应
        """
        # 构建结构化提示
        structured_prompt = f"""
{prompt}

请严格按照以下JSON Schema格式返回结果：

```json
{json.dumps(schema, ensure_ascii=False, indent=2)}
```

只返回JSON，不要包含其他文字说明。
"""
        
        try:
            response = await self.chat(
                message=structured_prompt,
                role=role,
                temperature=temperature
            )
            
            content = response.get("content", "")
            
            # 尝试解析JSON
            try:
                # 提取JSON部分
                if "```json" in content:
                    json_start = content.find("```json") + 7
                    json_end = content.find("```", json_start)
                    json_content = content[json_start:json_end].strip()
                else:
                    json_content = content.strip()
                    
                parsed_json = json.loads(json_content)
                
                response["parsed_json"] = parsed_json
                response["parse_success"] = True
                
            except json.JSONDecodeError as e:
                self.logger.warning(f"JSON解析失败: {str(e)}, 原始内容: {content[:200]}...")
                response["parsed_json"] = {}
                response["parse_success"] = False
                response["parse_error"] = str(e)
                
            return response
            
        except Exception as e:
            self.logger.error(f"结构化生成失败: {str(e)}")
            return {
                "content": "",
                "parsed_json": {},
                "parse_success": False,
                "error": str(e)
            }
            
    # ==================== 批量处理 ====================
    
    async def batch_chat(
        self,
        messages: List[str],
        role: str = "basic",
        temperature: float = 0.7,
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """
        批量聊天处理
        
        Args:
            messages: 消息列表
            role: LLM角色
            temperature: 温度参数
            max_concurrent: 最大并发数
            
        Returns:
            List[Dict[str, Any]]: 响应列表
        """
        import asyncio
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_message(message: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.chat(
                    message=message,
                    role=role,
                    temperature=temperature
                )
                
        try:
            tasks = [process_single_message(msg) for msg in messages]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常
            results = []
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    results.append({
                        "content": "",
                        "error": str(response),
                        "message_index": i
                    })
                else:
                    response["message_index"] = i
                    results.append(response)
                    
            self.logger.info(f"批量聊天完成: {len(messages)}条消息, 成功{len([r for r in results if 'error' not in r])}条")
            return results
            
        except Exception as e:
            self.logger.error(f"批量聊天失败: {str(e)}")
            return [{"content": "", "error": str(e), "message_index": i} for i in range(len(messages))]
            
    # ==================== 健康检查 ====================
    
    async def health_check(self) -> Dict[str, Any]:
        """
        LLM服务健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        health_status = {
            "service": "llm_service",
            "status": "healthy",
            "roles": {}
        }
        
        # 检查各个角色的LLM客户端
        roles = ["basic", "reasoning"]
        
        for role in roles:
            try:
                # 发送简单的测试消息
                response = await self.chat("测试", role=role, max_tokens=10)
                
                health_status["roles"][role] = {
                    "status": "healthy" if response.get("content") else "unhealthy",
                    "error": response.get("error")
                }
                
            except Exception as e:
                health_status["roles"][role] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                
        # 判断整体状态
        unhealthy_roles = [role for role, status in health_status["roles"].items() if status["status"] == "unhealthy"]
        if unhealthy_roles:
            health_status["status"] = "unhealthy"
            health_status["unhealthy_roles"] = unhealthy_roles
            
        return health_status
