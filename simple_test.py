"""
简化的LangGraph Agent测试脚本

测试基本的模块导入和创建功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, 'src')

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本模块导入...")
    
    try:
        # 测试基础模块
        from src.agents.common.base import AgentBase, WorkflowAgent
        print("✅ 通用基类导入成功")
        
        from src.agents.common.services import LLMService, DatabaseService, RedisService
        print("✅ 通用服务层导入成功")
        
        from src.agents.common.workflow import BaseAgentState, EventEmitter
        print("✅ 通用工作流组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本模块导入失败: {e}")
        return False


def test_travel_planner_imports():
    """测试旅行规划模块导入"""
    print("\n🔍 测试旅行规划模块导入...")
    
    try:
        # 测试核心模块
        from src.agents.travel_planner.core import IntentProcessor, POIAnalyzer, ItineraryComposer
        print("✅ 核心业务模块导入成功")
        
        # 测试工作流模块
        from src.agents.travel_planner.workflow import (
            TravelPlannerState, 
            create_travel_planner_state,
            TravelPlannerNodes
        )
        print("✅ 工作流模块导入成功")
        
        # 测试主Agent
        from src.agents.travel_planner import LangGraphTravelPlannerAgent
        print("✅ LangGraph Agent导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 旅行规划模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_imports():
    """测试数据模型导入"""
    print("\n🔍 测试数据模型导入...")
    
    try:
        from src.models.travel_models import TravelPlanRequest, POI, DailyPlan, TravelItinerary
        print("✅ 旅行模型导入成功")
        
        # 测试模型创建
        request = TravelPlanRequest(
            trace_id="test_001",
            user_id="test_user",
            query="测试查询"
        )
        print("✅ TravelPlanRequest创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_agent_creation():
    """测试Agent创建"""
    print("\n🔍 测试Agent创建...")
    
    try:
        from src.agents.travel_planner import LangGraphTravelPlannerAgent
        
        # 创建Agent实例
        agent = LangGraphTravelPlannerAgent()
        print(f"✅ Agent创建成功: {agent.agent_id}")
        
        # 测试基本方法
        agent_info = agent.get_agent_info()
        print(f"✅ Agent信息获取成功: {agent_info.get('agent_type')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_core_modules():
    """测试核心模块创建"""
    print("\n🔍 测试核心模块创建...")
    
    try:
        from src.agents.travel_planner.core import IntentProcessor, POIAnalyzer, ItineraryComposer
        
        # 创建核心模块实例
        intent_processor = IntentProcessor()
        print("✅ IntentProcessor创建成功")
        
        poi_analyzer = POIAnalyzer()
        print("✅ POIAnalyzer创建成功")
        
        itinerary_composer = ItineraryComposer()
        print("✅ ItineraryComposer创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_workflow_components():
    """测试工作流组件"""
    print("\n🔍 测试工作流组件...")
    
    try:
        from src.agents.travel_planner.workflow import (
            create_travel_planner_state,
            TravelPlannerNodes
        )
        
        # 创建状态
        state = create_travel_planner_state(
            trace_id="test_trace",
            user_id="test_user", 
            agent_id="test_agent",
            original_query="测试查询"
        )
        print("✅ 状态创建成功")
        
        # 创建节点
        nodes = TravelPlannerNodes()
        print("✅ 节点创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_langgraph():
    """检查LangGraph是否可用"""
    print("\n🔍 检查LangGraph可用性...")
    
    try:
        import langgraph
        print("✅ LangGraph已安装")
        return True
    except ImportError:
        print("❌ LangGraph未安装")
        print("   可以运行: pip install langgraph")
        return False


def main():
    """主测试函数"""
    print("🚀 简化版LangGraph Agent测试")
    print("=" * 50)
    
    tests = [
        ("基本模块导入", test_basic_imports),
        ("旅行规划模块导入", test_travel_planner_imports),
        ("数据模型导入", test_model_imports),
        ("Agent创建", test_agent_creation),
        ("核心模块创建", test_core_modules),
        ("工作流组件", test_workflow_components),
        ("LangGraph检查", check_langgraph)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！架构重构成功。")
    elif passed >= len(results) - 2:
        print("⚠️ 大部分测试通过，架构基本可用。")
    else:
        print("❌ 多个测试失败，需要进一步调试。")
    
    # 给出下一步建议
    if passed >= len(results) - 1:
        print("\n🚀 下一步建议:")
        print("1. 安装LangGraph: pip install langgraph")
        print("2. 运行完整测试: python test_langgraph_agent.py")
        print("3. 测试实际工作流执行")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
