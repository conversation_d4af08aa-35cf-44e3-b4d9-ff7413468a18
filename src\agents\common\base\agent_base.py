"""
Agent抽象基类

定义所有Agent的通用接口和基础功能
"""

import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, AsyncGenerator, Optional
from datetime import datetime


class AgentBase(ABC):
    """Agent抽象基类，定义所有Agent的通用接口"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化Agent基类
        
        Args:
            config: Agent配置字典
        """
        self.config = config or {}
        self.agent_id = self._generate_agent_id()
        self.created_at = datetime.now()
        
    def _generate_agent_id(self) -> str:
        """生成唯一的Agent实例ID"""
        return f"agent_{uuid.uuid4().hex[:8]}"
        
    @abstractmethod
    async def process_request(
        self, 
        request: Any, 
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理用户请求的主入口方法
        
        Args:
            request: 用户请求对象
            **kwargs: 额外参数
            
        Yields:
            Dict[str, Any]: 流式事件数据
        """
        pass
        
    @abstractmethod
    def validate_request(self, request: Any) -> bool:
        """
        验证请求参数有效性
        
        Args:
            request: 用户请求对象
            
        Returns:
            bool: 验证是否通过
        """
        pass
        
    def get_agent_info(self) -> Dict[str, Any]:
        """
        获取Agent基本信息
        
        Returns:
            Dict[str, Any]: Agent信息
        """
        return {
            "agent_id": self.agent_id,
            "agent_type": self.__class__.__name__,
            "created_at": self.created_at.isoformat(),
            "config": self.config
        }
        
    async def health_check(self) -> Dict[str, Any]:
        """
        Agent健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        return {
            "agent_id": self.agent_id,
            "status": "healthy",
            "timestamp": datetime.now().isoformat()
        }
